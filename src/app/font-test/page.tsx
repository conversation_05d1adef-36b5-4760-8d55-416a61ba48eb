// Font configuration utilities available if needed
// import { getFontClassName, getFontStyle } from '@/lib/fonts/config';

export default function FontTestPage() {
  return (
    <div className="min-h-screen p-8 space-y-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-center">
          字体配置测试页面 / Font Configuration Test
        </h1>

        {/* 英文字体测试 */}
        <section className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">
            English Font Test (Geist)
          </h2>
          <div className="space-y-4">
            <div className="font-brand text-3xl">
              Brand Title: Modern Technology Solutions
            </div>
            <div className="font-heading text-xl">
              Heading: Next.js 15 with React 19 - The Future of Web Development
            </div>
            <div className="font-body text-base">
              Body Text: This is a comprehensive test of our font configuration
              system. We are using <PERSON>eist as our primary Latin font, which
              provides excellent readability and modern aesthetics. The font
              features geometric design principles and is optimized for screen
              display across all devices.
            </div>
            <div className="font-caption text-sm">
              Caption: Performance metrics and user experience indicators
            </div>
          </div>
        </section>

        {/* 中文字体测试 */}
        <section lang="zh" className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">中文字体测试 (苹方)</h2>
          <div className="space-y-4">
            <div className="font-brand text-3xl">
              品牌标题：现代化技术解决方案
            </div>
            <div className="font-heading text-xl">
              内容标题：Next.js 15 与 React 19 - Web 开发的未来
            </div>
            <div className="font-body text-base">
              正文内容：这是我们字体配置系统的综合测试。我们使用苹方作为主要的中文字体，
              它提供了优秀的可读性和现代美学效果。该字体采用了现代设计原则，
              针对屏幕显示进行了优化，在所有设备上都能提供出色的视觉体验。
              字体的字间距和行高都经过精心调整，确保最佳的阅读舒适度。
            </div>
            <div className="font-caption text-sm">
              说明文字：性能指标和用户体验数据
            </div>
          </div>
        </section>

        {/* 字体大小测试 */}
        <section className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Font Size Scale Test</h2>
          <div className="space-y-2">
            <div className="text-xs">Extra Small (12px) - Navigation links</div>
            <div className="text-sm">Small (14px) - Captions and metadata</div>
            <div className="text-base">Base (16px) - Body text and content</div>
            <div className="text-lg">Large (18px) - Emphasized content</div>
            <div className="text-xl">Extra Large (20px) - Subheadings</div>
            <div className="text-2xl">2XL (24px) - Section headings</div>
            <div className="text-3xl">3XL (30px) - Page titles</div>
            <div className="text-4xl">4XL (36px) - Hero headings</div>
            <div className="text-5xl">5XL (48px) - Display text</div>
          </div>
        </section>

        {/* 字重测试 */}
        <section className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Font Weight Test</h2>
          <div className="space-y-2">
            <div className="font-light text-lg">
              Light (300) - Elegant and minimal
            </div>
            <div className="font-normal text-lg">
              Normal (400) - Standard body text
            </div>
            <div className="font-medium text-lg">
              Medium (500) - Emphasized content
            </div>
            <div className="font-semibold text-lg">
              Semibold (600) - Headings and titles
            </div>
            <div className="font-bold text-lg">
              Bold (700) - Strong emphasis
            </div>
            <div className="font-extrabold text-lg">
              Extra Bold (800) - Brand elements
            </div>
          </div>
        </section>

        {/* 中文字重测试 */}
        <section lang="zh" className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">中文字重测试</h2>
          <div className="space-y-2">
            <div className="font-light text-lg">细体 (300) - 优雅简约风格</div>
            <div className="font-normal text-lg">常规 (400) - 标准正文内容</div>
            <div className="font-medium text-lg">中等 (500) - 强调内容</div>
            <div className="font-semibold text-lg">半粗 (600) - 标题和重点</div>
            <div className="font-bold text-lg">粗体 (700) - 强烈强调</div>
            <div className="font-extrabold text-lg">特粗 (800) - 品牌元素</div>
          </div>
        </section>

        {/* 响应式测试提示 */}
        <section className="mb-12 p-6 border rounded-lg bg-blue-50">
          <h2 className="text-2xl font-semibold mb-4">响应式测试说明</h2>
          <div className="space-y-2 text-sm">
            <p>
              📱 <strong>移动端 (≤768px)</strong>:
              字体大小会自动调整，中文字体保持较大字号
            </p>
            <p>
              📟 <strong>平板端 (769px-1024px)</strong>:
              字体大小适中，行高增加提升可读性
            </p>
            <p>
              🖥️ <strong>桌面端 (≥1025px)</strong>:
              字体大小最大，启用高级字体特性
            </p>
            <p>
              🎨 <strong>字体特性</strong>:
              桌面端启用字距调整、连字等高级排版特性
            </p>
          </div>
        </section>

        {/* 特殊用途字体测试 */}
        <section className="mb-12 p-6 border rounded-lg">
          <h2 className="text-2xl font-semibold mb-4">Special Purpose Fonts</h2>
          <div className="space-y-4">
            <div>
              <h3 className="font-semibold mb-2">Monospace Font (Code)</h3>
              <code className="font-mono bg-gray-100 p-2 rounded">
                const fontConfig = &#123; family: &apos;Geist&apos;, weight: 400
                &#125;;
              </code>
            </div>
            <div>
              <h3 className="font-semibold mb-2">
                Numeric Font (Tabular Numbers)
              </h3>
              <div className="font-numeric space-y-1">
                <div>Price: $1,234.56</div>
                <div>Price: $9,876.54</div>
                <div>Price: $5,432.10</div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Quote Style</h3>
              <blockquote className="font-quote border-l-4 border-blue-500 pl-4">
                &ldquo;Typography is the craft of endowing human language with a
                durable visual form.&rdquo;
              </blockquote>
            </div>
          </div>
        </section>

        {/* 性能信息 */}
        <section className="mb-12 p-6 border rounded-lg bg-green-50">
          <h2 className="text-2xl font-semibold mb-4">性能优化信息</h2>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-semibold mb-2">英文字体 (Geist)</h3>
              <ul className="space-y-1">
                <li>✅ 可变字体技术</li>
                <li>✅ 文件大小: ~85KB</li>
                <li>✅ font-display: swap</li>
                <li>✅ 预加载优化</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">中文字体 (苹方)</h3>
              <ul className="space-y-1">
                <li>✅ 系统字体，无需下载</li>
                <li>✅ 零延迟加载</li>
                <li>✅ 完美的屏幕显示</li>
                <li>✅ 跨平台兼容</li>
              </ul>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
