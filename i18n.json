{"version": 1.8, "locale": {"source": "en", "targets": ["zh"]}, "buckets": {"mdx": {"include": ["content/en/*.mdx"]}, "markdown": {"include": ["content/en/*.md", "test-translation.md", "github-actions-test.md"]}, "json": {"include": ["i18n/[locale].json"]}}, "provider": {"id": "google", "model": "gemini-2.0-pro", "prompt": "You are a professional software localization expert. Translate each entry from {source} to {target} accurately. Preserve Markdown formatting, ICU/React placeholders such as {name} and plural rules like {{count}}. Keep technical terms untranslated."}, "$schema": "https://lingo.dev/schema/i18n.json"}