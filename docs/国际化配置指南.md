# 国际化配置指南

## 目录
- [概述](#概述)
- [技术架构](#技术架构)
- [环境配置](#环境配置)
- [配置文件](#配置文件)
- [开发指南](#开发指南)
- [部署配置](#部署配置)
- [维护指南](#维护指南)

## 概述

本项目基于 **Next.js 15 App Router** 实现国际化，支持英文和中文两种语言，使用 **Lingo.dev + Google Gemini** 实现自动化翻译。

### 技术特点
- **框架**：Next.js 15 App Router
- **路由**：基于文件夹的国际化路由 (`/en/`, `/zh/`)
- **翻译引擎**：Google Gemini 2.0 Pro
- **管理工具**：Lingo.dev CLI
- **自动化**：GitHub Actions CI/CD

## 技术架构

### 1. 国际化架构图
```mermaid
graph TD
    A[用户请求] --> B[Middleware]
    B --> C{语言检测}
    C -->|en| D[英文路由]
    C -->|zh| E[中文路由]
    D --> F[英文内容]
    E --> G[中文内容]
    F --> H[渲染页面]
    G --> H
    
    I[内容管理] --> J[MDX文件]
    I --> K[翻译文案]
    J --> L[Lingo.dev]
    K --> L
    L --> M[Google Gemini]
    M --> N[自动翻译]
```

### 2. 文件结构
```
src/
├── app/
│   ├── [locale]/           # 国际化路由
│   │   ├── page.tsx        # 首页
│   │   ├── products/       # 产品页面
│   │   └── layout.tsx      # 布局组件
│   └── globals.css
├── components/
│   └── navigation/
│       └── language-switcher.tsx  # 语言切换器
├── lib/
│   └── i18n/
│       ├── config.ts       # 国际化配置
│       ├── translations.ts # 翻译文案
│       └── use-translations.ts # 翻译Hook
├── middleware.ts           # 路由中间件
content/
├── en/                     # 英文内容
└── zh/                     # 中文内容
```

## 环境配置

### 1. 开发环境设置

#### 必需的环境变量
```bash
# .env.local
NEXT_PUBLIC_APP_URL=http://localhost:3000
GOOGLE_API_KEY=your_google_api_key_here
```

#### 可选的环境变量
```bash
# 分析工具（可选）
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# 其他分析工具
NEXT_PUBLIC_GA_ID=your_google_analytics_id
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id
```

### 2. Google API配置

#### 获取API Key
1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Gemini API
4. 创建API密钥
5. 复制密钥到环境变量

#### API Key验证
```bash
# 验证API Key格式
echo $GOOGLE_API_KEY | grep "^AIzaSy"

# 测试API连接
curl -H "Content-Type: application/json" \
     -d '{"contents":[{"parts":[{"text":"Hello"}]}]}' \
     "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$GOOGLE_API_KEY"
```

### 3. 开发工具安装
```bash
# 安装项目依赖
npm install

# 安装Lingo.dev CLI
npm install -D lingo.dev@latest

# 验证安装
npx lingo.dev --version
```

## 配置文件

### 1. i18n.json (Lingo.dev配置)
```json
{
  "version": 1.8,
  "locale": {
    "source": "en",
    "targets": ["zh"]
  },
  "buckets": {
    "mdx": {
      "include": ["content/en/*.mdx"]
    },
    "markdown": {
      "include": [
        "content/en/*.md",
        "test-translation.md",
        "github-actions-test.md"
      ]
    },
    "json": {
      "include": ["i18n/[locale].json"]
    }
  },
  "provider": {
    "id": "google",
    "model": "gemini-2.0-pro",
    "prompt": "You are a professional software localization expert. Translate each entry from {source} to {target} accurately. Preserve Markdown formatting, ICU/React placeholders such as {name} and plural rules like {{count}}. Keep technical terms untranslated."
  },
  "$schema": "https://lingo.dev/schema/i18n.json"
}
```

### 2. next.config.ts (Next.js配置)
```typescript
import type { NextConfig } from 'next';
import createMDX from '@next/mdx';

const nextConfig: NextConfig = {
  // MDX支持
  pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],
  
  // 注意：App Router不支持next.config.ts中的i18n配置
  // 国际化通过middleware.ts实现
  
  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
  },
  
  // 性能优化
  poweredByHeader: false,
  compress: true,
};

const withMDX = createMDX({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

export default withMDX(nextConfig);
```

### 3. middleware.ts (路由中间件)
```typescript
import { NextRequest, NextResponse } from 'next/server';
import { locales, defaultLocale, getLocaleFromPathname } from '@/lib/i18n/config';

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // 检查路径是否已包含语言前缀
  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (!pathnameHasLocale) {
    // 重定向到默认语言
    const locale = defaultLocale;
    return NextResponse.redirect(
      new URL(`/${locale}${pathname}`, request.url)
    );
  }
}

export const config = {
  matcher: [
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
```

### 4. 语言配置 (src/lib/i18n/config.ts)
```typescript
export const locales = ['en', 'zh'] as const;
export const defaultLocale = 'en' as const;

export type Locale = (typeof locales)[number];

export const localeNames: Record<Locale, string> = {
  en: 'English',
  zh: '中文',
};

export const localeLabels: Record<Locale, string> = {
  en: 'EN',
  zh: '中文',
};

// 工具函数
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const potentialLocale = segments[1];
  
  if (isValidLocale(potentialLocale)) {
    return potentialLocale;
  }
  
  return defaultLocale;
}
```

## 开发指南

### 1. 添加新语言

#### 步骤1：更新配置
```typescript
// src/lib/i18n/config.ts
export const locales = ['en', 'zh', 'ja'] as const; // 添加日文

export const localeNames: Record<Locale, string> = {
  en: 'English',
  zh: '中文',
  ja: '日本語', // 添加日文
};
```

#### 步骤2：更新翻译配置
```json
// i18n.json
{
  "locale": {
    "source": "en",
    "targets": ["zh", "ja"]
  }
}
```

#### 步骤3：创建内容目录
```bash
mkdir content/ja
```

### 2. 添加新的翻译文案

#### 在translations.ts中添加
```typescript
export const enTranslations: Translations = {
  // 现有内容...
  newFeature: {
    title: 'New Feature',
    description: 'Feature description',
    button: 'Try Now'
  }
};

export const zhTranslations: Translations = {
  // 现有内容...
  newFeature: {
    title: '新功能',
    description: '功能描述',
    button: '立即尝试'
  }
};
```

#### 在组件中使用
```typescript
import { useTranslations } from '@/lib/i18n/use-translations';

export function NewFeatureComponent({ locale }: { locale: Locale }) {
  const t = useTranslations(locale);
  
  return (
    <div>
      <h2>{t.newFeature.title}</h2>
      <p>{t.newFeature.description}</p>
      <button>{t.newFeature.button}</button>
    </div>
  );
}
```

### 3. 创建多语言页面
```typescript
// app/[locale]/new-page/page.tsx
import { Locale } from '@/lib/i18n/config';
import { useTranslations } from '@/lib/i18n/use-translations';

interface PageProps {
  params: { locale: Locale };
}

export default function NewPage({ params: { locale } }: PageProps) {
  const t = useTranslations(locale);
  
  return (
    <div>
      <h1>{t.newPage.title}</h1>
      <p>{t.newPage.content}</p>
    </div>
  );
}

// 生成静态参数
export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
```

## 部署配置

### 1. GitHub Actions设置

#### 配置Secrets
1. 访问 GitHub仓库 > Settings > Secrets and variables > Actions
2. 添加以下Secrets：
   - `GOOGLE_API_KEY`: Google API密钥

#### 工作流文件
```yaml
# .github/workflows/i18n.yml
name: 自动翻译
on:
  push:
    paths:
      - 'content/en/**'
      - '*.md'
  workflow_dispatch:

jobs:
  translate:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run translation
        uses: lingodotdev/lingo.dev@main
        with:
          api-key: ${{ secrets.GOOGLE_API_KEY }}
          config-path: './i18n.json'
```

### 2. Vercel部署配置
```json
// vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "env": {
    "GOOGLE_API_KEY": "@google-api-key"
  }
}
```

### 3. 环境变量配置
```bash
# 生产环境
NEXT_PUBLIC_APP_URL=https://your-domain.com
GOOGLE_API_KEY=your_production_api_key

# 预览环境
NEXT_PUBLIC_APP_URL=https://preview.your-domain.com
GOOGLE_API_KEY=your_preview_api_key
```

## 维护指南

### 1. 定期维护任务

#### 每周检查
```bash
# 检查翻译状态
npm run i18n:status

# 验证配置
npm run i18n:verify

# 更新依赖
npm update lingo.dev
```

#### 每月检查
```bash
# 检查API使用量
# 访问Google Cloud Console查看API调用统计

# 更新翻译模型
# 检查是否有新的Gemini模型版本

# 性能监控
npm run build
npm run test
```

### 2. 故障排除

#### 常见问题检查清单
- [ ] Google API Key是否有效
- [ ] Lingo.dev CLI版本是否最新
- [ ] i18n.json配置是否正确
- [ ] GitHub Secrets是否设置
- [ ] 网络连接是否正常

#### 调试命令
```bash
# 详细日志
npx lingo.dev i18n --verbose

# 配置验证
npm run i18n:verify

# 重置缓存
rm -rf .lingo/
npm install
```

### 3. 性能优化

#### 翻译缓存
```bash
# 启用翻译缓存（减少API调用）
# 在i18n.json中配置缓存策略
```

#### 批量处理
```bash
# 批量翻译多个文件
npx lingo.dev i18n --batch-size=10
```

#### 增量翻译
```bash
# 只翻译变更的文件
npx lingo.dev i18n --incremental
```

## 相关文档

- [文案编辑流程](./文案编辑流程.md) - 了解如何创建和编辑内容
- [翻译工作流程](./翻译工作流程.md) - 了解如何进行内容翻译

---

**文档维护**：本文档由技术团队维护，如有问题请联系DevOps负责人。
**最后更新**：2024年1月
