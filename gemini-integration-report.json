{"timestamp": "2025-06-13T09:05:38.313Z", "summary": {"pass": 4, "warning": 1, "fail": 0}, "details": [{"name": "Lingo.dev CLI Version", "status": "warning", "message": "⚠️  CLI版本:  (建议≥v0.101.0支持内置Gemini)", "action": "升级到lingo.dev@latest获得更好的Gemini支持"}, {"name": "i18n.json Provider", "status": "pass", "message": "✅ Google Gemini provider配置正确 (model: gemini-2.0-pro)"}, {"name": "Google API Key", "status": "pass", "message": "✅ Google API Key已正确配置"}, {"name": "GitHub Workflow", "status": "pass", "message": "✅ GitHub Actions工作流配置正确"}, {"name": "Lingo配置验证", "status": "pass", "message": "✅ Lingo配置验证通过"}], "ready": true}