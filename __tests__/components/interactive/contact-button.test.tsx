import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  EmailButton,
  PhoneButton,
  ContactButton,
} from '@/components/interactive/contact-button';

// Mock window.location
const mockLocation = {
  href: '',
  assign: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

describe('EmailButton', () => {
  beforeEach(() => {
    mockLocation.href = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(<EmailButton email="<EMAIL>">Send Email</EmailButton>);

    expect(
      screen.getByRole('button', { name: /send email/i })
    ).toBeInTheDocument();
  });

  it('opens email client when clicked', async () => {
    const user = userEvent.setup();

    render(<EmailButton email="<EMAIL>">Send Email</EmailButton>);

    const button = screen.getByRole('button', { name: /send email/i });
    await user.click(button);

    expect(mockLocation.href).toBe('mailto:<EMAIL>');
  });

  it('includes subject and body in email link', async () => {
    const user = userEvent.setup();

    render(
      <EmailButton
        email="<EMAIL>"
        subject="Test Subject"
        body="Test Body"
      >
        Send Email
      </EmailButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe(
      'mailto:<EMAIL>?subject=Test+Subject&body=Test+Body'
    );
  });

  it('shows icon when showIcon is true', () => {
    render(
      <EmailButton email="<EMAIL>" showIcon>
        Send Email
      </EmailButton>
    );

    // Check for Mail icon (lucide-react)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(
      <EmailButton email="<EMAIL>" className="custom-class">
        Send Email
      </EmailButton>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<EmailButton email="<EMAIL>">Send Email</EmailButton>);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute(
      'aria-label',
      'Send <NAME_EMAIL>'
    );
  });
});

describe('PhoneButton', () => {
  beforeEach(() => {
    mockLocation.href = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(<PhoneButton phone="******-123-4567">Call Now</PhoneButton>);

    expect(screen.getByText('Call Now')).toBeInTheDocument();
  });

  it('opens phone dialer when clicked', async () => {
    const user = userEvent.setup();

    render(<PhoneButton phone="******-123-4567">Call Now</PhoneButton>);

    const button = screen.getByText('Call Now');
    await user.click(button);

    expect(mockLocation.href).toBe('tel:******-123-4567');
  });

  it('shows icon when showIcon is true', () => {
    render(
      <PhoneButton phone="******-123-4567" showIcon>
        Call Now
      </PhoneButton>
    );

    // Check for Phone icon (lucide-react)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<PhoneButton phone="******-123-4567">Call Now</PhoneButton>);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Call ******-123-4567');
  });
});

describe('ContactButton', () => {
  beforeEach(() => {
    mockLocation.href = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(
      <ContactButton email="<EMAIL>" phone="******-123-4567">
        Contact Us
      </ContactButton>
    );

    expect(screen.getByText('Contact Us')).toBeInTheDocument();
  });

  it('uses email as preferred method by default', async () => {
    const user = userEvent.setup();

    render(
      <ContactButton email="<EMAIL>" phone="******-123-4567">
        Contact Us
      </ContactButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('mailto:<EMAIL>');
  });

  it('uses phone when preferredMethod is phone', async () => {
    const user = userEvent.setup();

    render(
      <ContactButton
        email="<EMAIL>"
        phone="******-123-4567"
        preferredMethod="phone"
      >
        Contact Us
      </ContactButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('tel:******-123-4567');
  });

  it('falls back to phone when email is not provided', async () => {
    const user = userEvent.setup();

    render(<ContactButton phone="******-123-4567">Contact Us</ContactButton>);

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('tel:******-123-4567');
  });

  it('falls back to email when phone is not provided', async () => {
    const user = userEvent.setup();

    render(
      <ContactButton email="<EMAIL>" preferredMethod="phone">
        Contact Us
      </ContactButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('mailto:<EMAIL>');
  });

  it('does nothing when neither email nor phone is provided', async () => {
    const user = userEvent.setup();
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

    render(<ContactButton>Contact Us</ContactButton>);

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('');
    expect(consoleSpy).toHaveBeenCalledWith(
      'ContactButton: No email or phone provided'
    );

    consoleSpy.mockRestore();
  });
});
