import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Search, Home, MapPin } from 'lucide-react';
import Link from 'next/link';
import { BackButton } from '@/components/interactive/back-button';

/**
 * 404 Not Found 页面
 * 当用户访问不存在的页面时显示
 */
export default function NotFoundPage() {
  // 由于这是静态页面，我们提供双语内容
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950 p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* 主要404卡片 */}
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900 rounded-full w-fit">
              <Search className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-4xl font-bold text-blue-800 dark:text-blue-200 mb-2">
              404
            </CardTitle>
            <CardTitle className="text-2xl text-blue-800 dark:text-blue-200">
              Page Not Found / 页面未找到
            </CardTitle>
            <CardDescription className="text-blue-600 dark:text-blue-300 text-lg">
              The page you&apos;re looking for doesn&apos;t exist.
              <br />
              您要查找的页面不存在。
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 操作按钮 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Link href="/" className="w-full">
                <Button className="w-full" size="lg">
                  <Home className="mr-2 h-4 w-4" />
                  Go Home / 返回首页
                </Button>
              </Link>
              <BackButton variant="outline" size="lg" className="w-full">
                Go Back / 返回上页
              </BackButton>
            </div>

            {/* 帮助信息 */}
            <div className="pt-4 border-t space-y-4">
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  What can you do? / 您可以做什么？
                </h3>
                <div className="grid md:grid-cols-2 gap-4 text-sm text-muted-foreground">
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      Check the URL for typos
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      Go back to the homepage
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      Use the navigation menu
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      Contact support if needed
                    </li>
                  </ul>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      检查URL是否有拼写错误
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      返回首页
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      使用导航菜单
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      如需要请联系支持
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            {/* 快速链接 */}
            <div className="pt-4 border-t">
              <h3 className="font-semibold mb-3">Quick Links / 快速链接</h3>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                <Link href="/en">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    Home (EN)
                  </Button>
                </Link>
                <Link href="/zh">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    首页 (中文)
                  </Button>
                </Link>
                <Link href="/en/products">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    Products
                  </Button>
                </Link>
                <Link href="/zh/products">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    产品
                  </Button>
                </Link>
                <Link href="/en/about">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    About
                  </Button>
                </Link>
                <Link href="/zh/about">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    关于我们
                  </Button>
                </Link>
                <Link href="/en/contact">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    Contact
                  </Button>
                </Link>
                <Link href="/zh/contact">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    联系我们
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 额外信息 */}
        <Card className="bg-muted/50">
          <CardContent className="p-4 text-center text-sm text-muted-foreground">
            <p>
              If you believe this is an error, please contact our support team.
              <br />
              如果您认为这是一个错误，请联系我们的支持团队。
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
