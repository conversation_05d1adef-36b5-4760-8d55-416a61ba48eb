import { translations, type Translations } from './translations';
import { type Locale } from './config';

/**
 * 获取指定语言的翻译对象
 * @param locale 语言代码
 * @returns 翻译对象
 */
export function getTranslations(locale: Locale): Translations {
  return translations[locale] || translations.en;
}

/**
 * 获取嵌套翻译键的值
 * @param obj 翻译对象
 * @param path 键路径，如 'home.title' 或 'features.nextjs.title'
 * @param params 参数对象，用于替换占位符
 * @returns 翻译后的字符串
 */
export function getNestedTranslation(
  obj: Record<string, unknown> | Translations,
  path: string,
  params?: Record<string, string | number>
): string {
  const keys = path.split('.');
  let result: unknown = obj;

  for (const key of keys) {
    if (
      result &&
      typeof result === 'object' &&
      result !== null &&
      key in result
    ) {
      result = (result as Record<string, unknown>)[key];
    } else {
      console.warn(`Translation key not found: ${path}`);
      return path; // 返回键名作为后备
    }
  }

  if (typeof result !== 'string') {
    console.warn(`Translation value is not a string: ${path}`);
    return path;
  }

  // 替换参数占位符
  if (params) {
    return result.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key]?.toString() || match;
    });
  }

  return result;
}

/**
 * 翻译函数类型
 */
export type TranslationFunction = {
  (key: string, params?: Record<string, string | number>): string;
  // 提供类型化的访问方式
  common: Translations['common'];
  navigation: Translations['navigation'];
  home: Translations['home'];
  products: Translations['products'];
  blog: Translations['blog'];
  about: Translations['about'];
  forms: Translations['forms'];
  meta: Translations['meta'];
};

/**
 * 创建翻译函数
 * @param locale 语言代码
 * @returns 翻译函数
 */
export function createTranslationFunction(locale: Locale): TranslationFunction {
  const t = getTranslations(locale);

  const translationFunction = (
    key: string,
    params?: Record<string, string | number>
  ): string => {
    return getNestedTranslation(t, key, params);
  };

  // 添加类型化的访问属性
  Object.defineProperty(translationFunction, 'common', {
    get: () => t.common,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'navigation', {
    get: () => t.navigation,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'home', {
    get: () => t.home,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'products', {
    get: () => t.products,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'blog', {
    get: () => t.blog,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'about', {
    get: () => t.about,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'forms', {
    get: () => t.forms,
    enumerable: true,
  });

  Object.defineProperty(translationFunction, 'meta', {
    get: () => t.meta,
    enumerable: true,
  });

  return translationFunction as TranslationFunction;
}

/**
 * 服务器端翻译函数（用于服务器组件）
 * @param locale 语言代码
 * @returns 翻译函数
 */
export function getServerTranslations(locale: Locale): TranslationFunction {
  return createTranslationFunction(locale);
}

/**
 * 客户端翻译 Hook（用于客户端组件）
 * 注意：这个 Hook 需要在客户端组件中使用，并且需要传入当前的 locale
 * @param locale 语言代码
 * @returns 翻译函数
 */
export function useClientTranslations(locale: Locale): TranslationFunction {
  return createTranslationFunction(locale);
}

/**
 * 格式化翻译字符串中的参数
 * @param template 模板字符串
 * @param params 参数对象
 * @returns 格式化后的字符串
 */
export function formatTranslation(
  template: string,
  params: Record<string, string | number>
): string {
  return template.replace(/\{(\w+)\}/g, (match, key) => {
    return params[key]?.toString() || match;
  });
}

/**
 * 验证翻译键是否存在
 * @param locale 语言代码
 * @param key 翻译键
 * @returns 是否存在
 */
export function hasTranslation(locale: Locale, key: string): boolean {
  const t = getTranslations(locale);
  const keys = key.split('.');
  let result: unknown = t;

  for (const k of keys) {
    if (
      result &&
      typeof result === 'object' &&
      result !== null &&
      k in result
    ) {
      result = (result as Record<string, unknown>)[k];
    } else {
      return false;
    }
  }

  return typeof result === 'string';
}

/**
 * 获取所有可用的翻译键
 * @param obj 翻译对象
 * @param prefix 前缀
 * @returns 翻译键数组
 */
export function getAllTranslationKeys(
  obj: Record<string, unknown> | Translations = translations.en,
  prefix = ''
): string[] {
  const keys: string[] = [];

  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (typeof value === 'string') {
      keys.push(fullKey);
    } else if (typeof value === 'object' && value !== null) {
      keys.push(...getAllTranslationKeys(value, fullKey));
    }
  }

  return keys;
}
