# 翻译工作流程指南

## 目录
- [概述](#概述)
- [翻译架构](#翻译架构)
- [工作流程](#工作流程)
- [工具使用](#工具使用)
- [质量控制](#质量控制)
- [自动化流程](#自动化流程)
- [故障排除](#故障排除)

## 概述

本项目使用 **Lingo.dev + Google Gemini 2.0 Pro** 的AI翻译方案，实现从英文到中文的自动化翻译工作流程。

### 翻译范围
- **MDX内容文件**：`content/en/*.mdx` → `content/zh/*.mdx`
- **Markdown文档**：`*.md` 文件
- **界面文案**：`src/lib/i18n/translations.ts` 中的文案

### 支持语言
- **源语言**：英文 (en)
- **目标语言**：中文 (zh)

## 翻译架构

### 1. 技术栈
```mermaid
graph TD
    A[英文内容] --> B[Lingo.dev CLI]
    B --> C[Google Gemini 2.0 Pro]
    C --> D[AI翻译]
    D --> E[格式验证]
    E --> F[中文内容]
    F --> G[质量审核]
    G --> H[发布]
```

### 2. 文件组织
```
project/
├── content/
│   ├── en/                 # 英文原始内容
│   │   ├── home.mdx
│   │   └── products.mdx
│   └── zh/                 # 中文翻译内容
│       ├── home.mdx
│       └── products.mdx
├── src/lib/i18n/
│   └── translations.ts     # 界面文案翻译
├── i18n.json              # Lingo.dev配置
└── .github/workflows/
    └── i18n.yml           # 自动化翻译工作流
```

## 工作流程

### 1. 翻译准备阶段
| 步骤 | 操作 | 负责人 | 工具 |
|------|------|--------|------|
| 内容审核 | 确认英文内容完整准确 | 内容编辑 | 人工审核 |
| 格式检查 | 验证Markdown格式正确 | 开发者 | Prettier |
| 配置验证 | 检查翻译配置 | 开发者 | verify-gemini-integration.ts |

### 2. 翻译执行阶段

#### 2.1 手动翻译
```bash
# 1. 检查翻译状态
npm run i18n:status

# 2. 执行翻译
npm run i18n:translate

# 3. 验证翻译结果
npm run validate:translations
```

#### 2.2 自动翻译（推荐）
```bash
# 提交英文内容到GitHub
git add content/en/
git commit -m "feat: 添加新的英文内容"
git push origin main

# GitHub Actions自动触发翻译
# 翻译完成后自动创建PR
```

### 3. 质量审核阶段
| 检查项 | 标准 | 工具/方法 |
|--------|------|-----------|
| 格式完整性 | Markdown格式保持完整 | 自动验证 |
| 术语准确性 | 技术术语保持原文 | 人工审核 |
| 语言流畅性 | 中文表达自然流畅 | 人工审核 |
| 链接有效性 | 所有链接正常工作 | 自动检查 |

### 4. 发布阶段
```bash
# 1. 审核翻译PR
# 2. 合并到主分支
git merge translation-update

# 3. 部署到生产环境
npm run build
npm run deploy
```

## 工具使用

### 1. Lingo.dev CLI

#### 安装和配置
```bash
# 安装CLI
npm install -D lingo.dev@latest

# 检查版本
npx lingo.dev --version

# 查看配置
npx lingo.dev show config
```

#### 常用命令
```bash
# 查看翻译状态
npx lingo.dev status

# 执行翻译
npx lingo.dev i18n

# CI模式翻译
npx lingo.dev ci

# 显示配置信息
npx lingo.dev show
```

### 2. 配置文件 (i18n.json)
```json
{
  "version": 1.8,
  "locale": {
    "source": "en",
    "targets": ["zh"]
  },
  "buckets": {
    "mdx": {
      "include": ["content/en/*.mdx"]
    },
    "markdown": {
      "include": [
        "content/en/*.md",
        "test-translation.md",
        "github-actions-test.md"
      ]
    }
  },
  "provider": {
    "id": "google",
    "model": "gemini-2.0-pro",
    "prompt": "You are a professional software localization expert. Translate each entry from {source} to {target} accurately. Preserve Markdown formatting, ICU/React placeholders such as {name} and plural rules like {{count}}. Keep technical terms untranslated."
  }
}
```

### 3. 环境变量配置
```bash
# .env.local (本地开发)
GOOGLE_API_KEY=your_google_api_key_here

# GitHub Secrets (CI/CD)
GOOGLE_API_KEY=your_google_api_key_here
```

## 质量控制

### 1. 翻译质量标准

#### 格式保持
- ✅ Markdown语法完整保留
- ✅ 代码块不被翻译
- ✅ 链接地址保持不变
- ✅ 图片路径保持不变
- ✅ frontmatter保持不变

#### 内容准确性
- ✅ 技术术语保持英文原文
- ✅ 产品名称保持原文
- ✅ API名称保持原文
- ✅ 文件名和路径保持原文

#### 语言质量
- ✅ 中文表达自然流畅
- ✅ 符合中文语法习惯
- ✅ 专业术语使用准确
- ✅ 语调与原文一致

### 2. 验证流程
```bash
# 1. 自动格式验证
npm run format:check

# 2. 翻译状态检查
npm run i18n:status

# 3. 构建测试
npm run build

# 4. 本地预览
npm run dev
```

### 3. 人工审核清单
- [ ] 翻译内容是否准确传达原意
- [ ] 技术术语是否保持原文
- [ ] 中文表达是否自然流畅
- [ ] 格式是否完整保留
- [ ] 链接是否正常工作
- [ ] 代码示例是否完整

## 自动化流程

### 1. GitHub Actions工作流
```yaml
# .github/workflows/i18n.yml
name: 自动翻译
on:
  push:
    paths:
      - 'content/en/**'
      - '*.md'
  workflow_dispatch:

jobs:
  translate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: lingodotdev/lingo.dev@main
        with:
          api-key: ${{ secrets.GOOGLE_API_KEY }}
          config-path: './i18n.json'
```

### 2. 触发条件
- **自动触发**：英文内容文件发生变更
- **手动触发**：通过GitHub Actions界面手动执行
- **定时触发**：可配置定时检查更新

### 3. 输出结果
- **成功**：自动创建包含翻译内容的PR
- **失败**：发送通知邮件，包含错误详情
- **部分成功**：标记问题文件，翻译其他文件

## 故障排除

### 1. 常见问题

#### Q1: 翻译命令执行失败
```bash
# 检查CLI版本
npx lingo.dev --version

# 更新到最新版本
npm install -D lingo.dev@latest

# 验证配置
npm run i18n:verify
```

#### Q2: Google API Key错误
```bash
# 检查环境变量
echo $GOOGLE_API_KEY

# 验证API Key格式（应以AIzaSy开头）
# 确认API Key权限包含Gemini API
```

#### Q3: 翻译格式丢失
```bash
# 检查i18n.json中的prompt配置
# 确保包含格式保护指令
"prompt": "...Preserve Markdown formatting..."
```

#### Q4: 技术术语被翻译
```bash
# 更新prompt，添加术语保护
"prompt": "...Keep technical terms untranslated..."
```

### 2. 调试步骤
```bash
# 1. 验证环境配置
npm run i18n:verify

# 2. 检查文件权限
ls -la content/

# 3. 清理缓存
rm -rf .lingo/
rm -rf node_modules/.cache/

# 4. 重新安装依赖
npm install

# 5. 测试翻译
npm run i18n:translate
```

### 3. 日志分析
```bash
# 查看详细日志
npx lingo.dev i18n --verbose

# 检查GitHub Actions日志
# 访问 GitHub > Actions > 查看具体工作流日志
```

### 4. 紧急处理
```bash
# 如果自动翻译失败，手动处理：
# 1. 复制英文文件到中文目录
cp content/en/file.mdx content/zh/file.mdx

# 2. 手动翻译关键内容
# 3. 提交临时版本
git add content/zh/
git commit -m "temp: 手动翻译临时版本"

# 4. 后续修复自动化问题
```

## 相关文档

- [文案编辑流程](./文案编辑流程.md) - 了解如何创建和编辑内容
- [国际化配置指南](./国际化配置指南.md) - 了解技术配置和维护

---

**文档维护**：本文档由开发团队维护，如有问题请联系技术负责人。
**最后更新**：2024年1月
