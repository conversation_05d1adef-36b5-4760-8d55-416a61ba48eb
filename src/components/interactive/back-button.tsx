'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * 返回按钮组件
 *
 * 专门用于浏览器历史返回功能的客户端组件
 */

// 安全的 Button props，排除所有函数类型
type SafeButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  | 'onClick'
  | 'onMouseDown'
  | 'onMouseUp'
  | 'onKeyDown'
  | 'onKeyUp'
  | 'onFocus'
  | 'onBlur'
  | 'onSubmit'
>;

interface BackButtonProps extends SafeButtonProps {
  /**
   * 是否显示图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function BackButton({
  showIcon = true,
  iconPosition = 'left',
  children,
  className,
  ...props
}: BackButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      // 检查是否有历史记录可以返回
      if (window.history.length > 1) {
        window.history.back();
      } else {
        // 如果没有历史记录，跳转到首页
        window.location.href = '/';
      }
    },
    []
  );

  const icon = showIcon ? <ArrowLeft className="h-4 w-4" /> : null;

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label="Go back to previous page"
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}
