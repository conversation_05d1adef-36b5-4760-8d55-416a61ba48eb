import { type NavigationItem } from '@/types/i18n';

export const navigationItems: NavigationItem[] = [
  {
    href: '/',
    label: {
      en: 'Home',
      zh: '首页',
    },
    description: {
      en: 'Welcome to Tucsenberg Web',
      zh: '欢迎来到 Tucsenberg Web',
    },
  },
  {
    href: '/products',
    label: {
      en: 'Products',
      zh: '产品',
    },
    description: {
      en: 'Explore our products and solutions',
      zh: '探索我们的产品和解决方案',
    },
    children: [
      {
        href: '/products',
        label: {
          en: 'All Products',
          zh: '所有产品',
        },
        description: {
          en: 'View all flood protection solutions',
          zh: '查看所有防洪保护解决方案',
        },
      },
      {
        href: '/products/water-absorbing-bags',
        label: {
          en: 'Water Absorbing Bags',
          zh: '吸水膨胀袋',
        },
        description: {
          en: 'Rapid deployment flood protection',
          zh: '快速部署防洪保护',
        },
      },
      {
        href: '/products/abs-flood-barriers',
        label: {
          en: 'ABS Flood Barriers',
          zh: 'ABS防洪板',
        },
        description: {
          en: 'Modular flood protection system',
          zh: '模块化防洪保护系统',
        },
      },
      {
        href: '/products/aluminum-water-barriers',
        label: {
          en: 'Aluminum Water Barriers',
          zh: '铝合金挡水板',
        },
        description: {
          en: 'Premium custom flood barriers',
          zh: '高端定制防洪板',
        },
      },
    ],
  },
  {
    href: '/blog',
    label: {
      en: 'Blog',
      zh: '博客',
    },
    description: {
      en: 'Latest news and insights',
      zh: '最新资讯和见解',
    },
  },
  {
    href: '/about',
    label: {
      en: 'About',
      zh: '关于',
    },
    description: {
      en: 'Learn more about our company',
      zh: '了解更多关于我们公司',
    },
  },
  {
    href: '/contact',
    label: {
      en: 'Contact',
      zh: '联系',
    },
    description: {
      en: 'Get in touch with us',
      zh: '与我们取得联系',
    },
  },
];

export function getLocalizedHref(href: string, locale: string): string {
  if (locale === 'en') {
    return href;
  }
  return `/${locale}${href}`;
}
