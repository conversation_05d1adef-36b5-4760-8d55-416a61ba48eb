# 项目架构文档

## 目录
- [概述](#概述)
- [技术架构](#技术架构)
- [系统架构](#系统架构)
- [目录结构](#目录结构)
- [核心模块](#核心模块)
- [数据流](#数据流)
- [设计原则](#设计原则)
- [技术选型](#技术选型)

## 概述

Tucsenberg Web 1.0 是一个基于 Next.js 15 和 React 19 的现代化企业级前端开发框架，专注于提供完整的多语言Web应用解决方案。

### 项目特点
- **现代化技术栈**：采用最新的 React 19 和 Next.js 15
- **企业级架构**：支持大规模应用开发和部署
- **AI友好设计**：针对AI辅助开发进行优化
- **多语言支持**：完整的国际化和自动翻译方案
- **安全优先**：企业级安全配置和防护

## 技术架构

### 1. 整体架构图
```mermaid
graph TB
    subgraph "前端层"
        A[React 19 组件]
        B[Next.js 15 App Router]
        C[TypeScript 类型系统]
    end
    
    subgraph "状态管理层"
        D[Zustand 全局状态]
        E[React Query 数据缓存]
        F[React Hook Form 表单状态]
    end
    
    subgraph "UI层"
        G[shadcn/ui 组件库]
        H[Tailwind CSS 样式]
        I[Framer Motion 动画]
    end
    
    subgraph "国际化层"
        J[Middleware 路由]
        K[翻译文案管理]
        L[Lingo.dev 自动翻译]
    end
    
    subgraph "构建层"
        M[TypeScript 编译]
        N[ESLint 代码检查]
        O[Vitest 测试框架]
    end
    
    A --> D
    A --> G
    B --> J
    D --> E
    G --> H
    L --> K
```

### 2. 技术栈详情

#### 核心框架
- **Next.js 15**：React全栈框架，使用App Router
- **React 19**：用户界面库，支持最新特性
- **TypeScript 5.8+**：类型安全的JavaScript超集

#### 状态管理
- **Zustand 5.0+**：轻量级状态管理库
- **React Query 5.80+**：服务端状态管理和数据获取
- **React Hook Form**：高性能表单库

#### UI和样式
- **shadcn/ui**：基于Radix UI的组件库
- **Tailwind CSS 4.1+**：实用优先的CSS框架
- **Framer Motion**：动画库
- **Lucide React**：图标库

#### 开发工具
- **Vitest**：现代化测试框架
- **ESLint**：代码质量检查
- **Prettier**：代码格式化
- **Husky**：Git hooks管理

## 系统架构

### 1. 应用架构
```mermaid
graph LR
    subgraph "客户端"
        A[浏览器]
        B[React应用]
        C[状态管理]
    end
    
    subgraph "服务端"
        D[Next.js服务器]
        E[中间件层]
        F[API路由]
    end
    
    subgraph "外部服务"
        G[Google Gemini API]
        H[分析服务]
        I[CDN]
    end
    
    A --> B
    B --> C
    B --> D
    D --> E
    E --> F
    F --> G
    D --> H
    A --> I
```

### 2. 部署架构
```mermaid
graph TB
    subgraph "开发环境"
        A[本地开发]
        B[热重载]
        C[开发工具]
    end
    
    subgraph "CI/CD"
        D[GitHub Actions]
        E[自动测试]
        F[自动构建]
    end
    
    subgraph "生产环境"
        G[Vercel/云平台]
        H[CDN分发]
        I[监控告警]
    end
    
    A --> D
    D --> E
    E --> F
    F --> G
    G --> H
    G --> I
```

## 目录结构

### 1. 根目录结构
```
project/
├── src/                    # 源代码目录
│   ├── app/               # Next.js App Router
│   ├── components/        # React组件
│   ├── lib/              # 工具库和配置
│   ├── hooks/            # 自定义Hooks
│   ├── stores/           # Zustand状态管理
│   ├── styles/           # 全局样式
│   ├── types/            # TypeScript类型定义
│   └── templates/        # 组件模板
├── content/              # MDX内容文件
│   ├── en/              # 英文内容
│   └── zh/              # 中文内容
├── public/              # 静态资源
├── docs/                # 项目文档
├── __tests__/           # 测试文件
├── scripts/             # 构建和工具脚本
└── .github/             # GitHub配置
```

### 2. 核心目录详解

#### src/app/ - App Router结构
```
app/
├── [locale]/            # 国际化路由
│   ├── layout.tsx       # 根布局
│   ├── page.tsx         # 首页
│   ├── products/        # 产品页面
│   └── globals.css      # 全局样式
└── api/                 # API路由（如需要）
```

#### src/components/ - 组件结构
```
components/
├── ui/                  # shadcn/ui基础组件
├── navigation/          # 导航相关组件
├── forms/              # 表单组件
├── layout/             # 布局组件
└── features/           # 功能特定组件
```

#### src/lib/ - 工具库结构
```
lib/
├── i18n/               # 国际化配置
├── utils/              # 工具函数
├── validations/        # 数据验证
├── constants/          # 常量定义
└── security/           # 安全相关配置
```

## 核心模块

### 1. 国际化模块
```typescript
// 核心配置
export const locales = ['en', 'zh'] as const;
export type Locale = (typeof locales)[number];

// 路由处理
middleware.ts -> 语言检测和重定向
[locale]/ -> 动态路由结构

// 翻译管理
translations.ts -> 静态翻译文案
Lingo.dev -> 自动翻译工具
```

### 2. 状态管理模块
```typescript
// Zustand全局状态
interface AppState {
  theme: 'light' | 'dark' | 'system';
  locale: Locale;
  user: User | null;
}

// React Query数据获取
const queryClient = new QueryClient({
  defaultOptions: {
    queries: { staleTime: 5 * 60 * 1000 }
  }
});
```

### 3. 组件系统
```typescript
// 基础组件（shadcn/ui）
Button, Input, Dialog, DropdownMenu...

// 复合组件
Navigation, LanguageSwitcher, ThemeToggle...

// 页面组件
HomePage, ProductsPage, Layout...
```

### 4. 安全模块
```typescript
// 中间件安全
CORS配置, 安全头部, CSP策略

// 环境变量验证
@t3-oss/env-nextjs -> 类型安全的环境变量

// 输入验证
Zod -> 数据验证和类型推断
```

## 数据流

### 1. 用户交互流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 组件
    participant S as 状态管理
    participant A as API
    
    U->>C: 用户操作
    C->>S: 更新状态
    S->>A: 发起请求
    A->>S: 返回数据
    S->>C: 状态变更
    C->>U: 界面更新
```

### 2. 国际化数据流
```mermaid
sequenceDiagram
    participant U as 用户
    participant M as Middleware
    participant P as 页面
    participant T as 翻译系统
    
    U->>M: 访问页面
    M->>M: 检测语言
    M->>P: 重定向到语言路由
    P->>T: 获取翻译文案
    T->>P: 返回本地化内容
    P->>U: 渲染页面
```

## 设计原则

### 1. 架构原则
- **单一职责**：每个模块专注于特定功能
- **开放封闭**：对扩展开放，对修改封闭
- **依赖倒置**：依赖抽象而非具体实现
- **接口隔离**：使用小而专一的接口

### 2. 代码原则
- **类型安全**：严格的TypeScript类型检查
- **函数式编程**：优先使用纯函数和不可变数据
- **组件化**：可复用的组件设计
- **测试驱动**：完整的测试覆盖

### 3. 性能原则
- **代码分割**：按需加载和懒加载
- **缓存策略**：合理的数据缓存
- **图片优化**：现代图片格式和响应式
- **构建优化**：Tree shaking和压缩

## 技术选型

### 1. 选型理由

#### Next.js 15
- **优势**：全栈框架、App Router、性能优化
- **适用场景**：企业级应用、SEO要求高
- **替代方案**：Vite + React Router（更轻量但功能较少）

#### React 19
- **优势**：最新特性、性能提升、并发渲染
- **适用场景**：现代化应用开发
- **注意事项**：需要适配新的API和最佳实践

#### Zustand
- **优势**：轻量级、TypeScript友好、简单易用
- **适用场景**：中小型状态管理
- **替代方案**：Redux Toolkit（更复杂但功能更强）

#### shadcn/ui
- **优势**：现代设计、可定制、无障碍支持
- **适用场景**：快速原型和产品开发
- **替代方案**：Ant Design、Material-UI（更重但功能更全）

### 2. 技术债务管理
- **定期更新**：保持依赖项的最新版本
- **性能监控**：持续监控应用性能
- **代码审查**：确保代码质量和一致性
- **文档维护**：保持文档与代码同步

## 相关文档

### 开发相关
- [开发环境搭建指南](./开发环境搭建指南.md) - 了解如何配置开发环境
- [API集成文档](./API集成文档.md) - 了解外部服务集成
- [国际化配置指南](./国际化配置指南.md) - 了解多语言配置

### 运维相关
- [部署运维指南](./部署运维指南.md) - 了解部署和运维流程
- [翻译工作流程](./翻译工作流程.md) - 了解翻译自动化

---

**文档维护**：本文档由架构团队维护，如有问题请联系技术负责人。
**最后更新**：2024年1月
