import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from 'next/font/google';
import './globals.css';
import { ReactQueryProvider } from '@/lib/config/react-query';
import { env } from '@/env';
import { getAllFontVariables } from '@/lib/fonts/config';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: true,
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
  preload: false,
});

export const metadata: Metadata = {
  title: 'Tucsenberg Web 1.0',
  description: '现代化前端技术方案 - 基于 Next.js 15 和 React 19',
  keywords: ['Next.js', 'React', 'TypeScript', 'Tailwind CSS'],
  authors: [{ name: '<PERSON>csenberg Team' }],
  creator: '<PERSON><PERSON><PERSON>',
  publisher: '<PERSON><PERSON><PERSON>',
  metadataBase: new URL(env.NEXT_PUBLIC_APP_URL),
  openGraph: {
    title: 'Tucsenberg Web 1.0',
    description: '现代化前端技术方案 - 基于 Next.js 15 和 React 19',
    url: env.NEXT_PUBLIC_APP_URL,
    siteName: 'Tucsenberg Web',
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Tucsenberg Web 1.0',
    description: '现代化前端技术方案 - 基于 Next.js 15 和 React 19',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // 获取所有字体变量，包括多语言字体
  const fontVariables = `${geistSans.variable} ${geistMono.variable} ${getAllFontVariables()}`;

  return (
    <html suppressHydrationWarning>
      <body className={`${fontVariables} antialiased`} suppressHydrationWarning>
        <ReactQueryProvider>{children}</ReactQueryProvider>
      </body>
    </html>
  );
}
