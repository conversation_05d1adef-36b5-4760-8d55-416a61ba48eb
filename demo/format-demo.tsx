// 🎨 Prettier 格式化演示文件
// 这个文件故意写成不规范的格式，用于演示自动格式化效果
//
// 📝 使用方法：
// 1. 复制此文件到 src/ 目录
// 2. 运行 git add . && git commit -m "test formatting"
// 3. 观察 Prettier 自动格式化效果

// ❌ 格式化前：不规范的代码风格
interface User{name:string;age:number;email:string;isActive?:boolean}

const users:User[]=[{name:"<PERSON>",age:30,email:"<EMAIL>",isActive:true},{name:"<PERSON>",age:25,email:"<EMAIL>"},{name:"<PERSON>",age:35,email:"<EMAIL>",isActive:false}]

// ❌ 不规范的函数定义（缺少空格、换行）
const getUserById=(id:number):User|undefined=>{
return users.find(user=>user.name.length>id)
}

const getActiveUsers=():User[]=>{
return users.filter(user=>user.isActive===true)
}

// ❌ 不规范的对象定义（缺少空格、换行）
const config={
host:"localhost",
port:3000,
debug:true,
features:["auth","api","ui","dashboard"],
database:{host:"db.localhost",port:5432,name:"app_db"}
}

// ❌ 不规范的数组操作（缺少换行、空格）
const processUsers=()=>{
const result=users.map(user=>{
return{
...user,
displayName:user.name.toUpperCase(),
isAdult:user.age>=18,
emailDomain:user.email.split("@")[1]
}
}).filter(user=>user.isAdult&&user.isActive)

return result
}

// ❌ 不规范的条件语句（缺少空格、换行）
const checkUserAccess=(user:User)=>{
if(user.age>18&&user.email.includes("@")&&user.isActive){
console.log("Access granted for:",user.name)
return true
}else{
console.log("Access denied for:",user.name)
return false
}
}

// ❌ 不规范的异步函数
const fetchUserData=async(userId:number)=>{
try{
const response=await fetch(`/api/users/${userId}`)
const data=await response.json()
return data
}catch(error){
console.error("Error fetching user:",error)
return null
}
}

// ❌ 不规范的类定义
class UserManager{
private users:User[]=[]
constructor(initialUsers:User[]){
this.users=initialUsers
}
addUser(user:User):void{
this.users.push(user)
}
removeUser(id:number):boolean{
const index=this.users.findIndex(user=>user.name.length===id)
if(index>-1){
this.users.splice(index,1)
return true
}
return false
}
}

// ❌ 不规范的导出
export type{User}
export{users,getUserById,getActiveUsers,config,processUsers,checkUserAccess,fetchUserData,UserManager}
