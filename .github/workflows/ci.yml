name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # 代码质量检查
  quality-check:
    name: Code Quality Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: TypeScript type checking
      run: npm run typecheck
      
    - name: ESLint checking
      run: npm run lint

    - name: AI Anti-patterns checking
      run: npm run lint:ai-patterns

    - name: Prettier formatting check
      run: npx prettier --check "src/**/*.{ts,tsx,js,jsx,json,css,md}"

  # 组件边界测试
  component-tests:
    name: Component Boundary Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run component tests
      run: npm run test:components
      
    - name: Run static generation tests
      run: npm run test:static
      
    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: component-test-results
        path: coverage/

  # 集成测试
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run integration tests
      run: npm run test:integration
      
    - name: Generate test coverage
      run: npm run test:coverage
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: integration
        name: integration-tests

  # 构建验证
  build-validation:
    name: Build Validation
    runs-on: ubuntu-latest
    needs: [quality-check, component-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run full build validation
      run: npm run validate:build
      
    - name: Check build output
      run: |
        if [ ! -d ".next" ]; then
          echo "Build output directory not found"
          exit 1
        fi
        echo "Build completed successfully"
        
    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: build-output
        path: .next/

  # 国际化验证
  i18n-validation:
    name: Internationalization Validation
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Validate translations
      run: npm run validate:translations
      
    - name: Check i18n configuration
      run: npm run check:i18n

  # 安全扫描
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run npm audit
      run: npm audit --audit-level=moderate
      
    - name: Run security scan with Snyk
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

  # 部署准备
  deployment-ready:
    name: Deployment Ready
    runs-on: ubuntu-latest
    needs: [quality-check, component-tests, integration-tests, build-validation, i18n-validation]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download build artifacts
      uses: actions/download-artifact@v4
      with:
        name: build-output
        path: .next/
        
    - name: Verify deployment readiness
      run: |
        echo "✅ All checks passed"
        echo "✅ Build artifacts available"
        echo "✅ Ready for deployment"
        
    - name: Create deployment summary
      run: |
        echo "## 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Quality Check**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- **AI Anti-patterns Check**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- **Component Tests**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- **Integration Tests**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- **Build Validation**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "- **I18n Validation**: ✅ Passed" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "🎉 **Ready for production deployment!**" >> $GITHUB_STEP_SUMMARY

  # 清理
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    needs: [deployment-ready]
    if: always()
    
    steps:
    - name: Clean up artifacts
      uses: actions/github-script@v7
      with:
        script: |
          const artifacts = await github.rest.actions.listWorkflowRunArtifacts({
            owner: context.repo.owner,
            repo: context.repo.repo,
            run_id: context.runId,
          });
          
          // Keep only the latest 5 artifacts
          const artifactsToDelete = artifacts.data.artifacts.slice(5);
          
          for (const artifact of artifactsToDelete) {
            await github.rest.actions.deleteArtifact({
              owner: context.repo.owner,
              repo: context.repo.repo,
              artifact_id: artifact.id,
            });
          }
