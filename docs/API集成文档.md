# API集成文档

## 目录
- [概述](#概述)
- [Google Gemini API](#google-gemini-api)
- [分析服务集成](#分析服务集成)
- [内部API设计](#内部api设计)
- [中间件配置](#中间件配置)
- [错误处理](#错误处理)
- [安全配置](#安全配置)
- [监控和日志](#监控和日志)

## 概述

本项目集成了多个外部API服务，主要用于AI翻译、用户分析和性能监控。所有API集成都遵循安全最佳实践和错误处理标准。

### 集成服务列表
- **Google Gemini API** - AI翻译和内容生成
- **PostHog** - 产品分析和用户行为追踪
- **Google Analytics** - 网站流量分析
- **Hotjar** - 用户体验分析
- **Vercel Analytics** - 性能监控

## Google Gemini API

### 1. 配置和认证

#### API密钥配置
```bash
# 环境变量配置
GOOGLE_API_KEY=AIzaSy...your_api_key_here

# 验证API密钥格式
echo $GOOGLE_API_KEY | grep "^AIzaSy" && echo "✅ 格式正确" || echo "❌ 格式错误"
```

#### 获取API密钥步骤
```bash
# 1. 访问 Google AI Studio
open https://aistudio.google.com/

# 2. 创建或选择项目
# 3. 启用 Gemini API
# 4. 创建 API 密钥
# 5. 设置使用限制和配额
```

### 2. 翻译服务集成

#### Lingo.dev 配置
```json
// i18n.json
{
  "version": 1.8,
  "locale": {
    "source": "en",
    "targets": ["zh"]
  },
  "provider": {
    "id": "google",
    "model": "gemini-2.0-pro",
    "prompt": "You are a professional software localization expert. Translate each entry from {source} to {target} accurately. Preserve Markdown formatting, ICU/React placeholders such as {name} and plural rules like {{count}}. Keep technical terms untranslated."
  }
}
```

#### API调用示例
```typescript
// lib/gemini/client.ts
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY!);

export async function translateText(
  text: string,
  targetLanguage: string
): Promise<string> {
  try {
    const model = genAI.getGenerativeModel({ model: 'gemini-2.0-pro' });
    
    const prompt = `Translate the following text to ${targetLanguage}. 
    Preserve all Markdown formatting and technical terms:
    
    ${text}`;
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    
    return response.text();
  } catch (error) {
    console.error('Translation error:', error);
    throw new Error('Translation failed');
  }
}
```

### 3. 使用限制和配额

#### 配额管理
```typescript
// lib/gemini/quota.ts
interface QuotaConfig {
  requestsPerMinute: number;
  requestsPerDay: number;
  tokensPerRequest: number;
}

const QUOTA_LIMITS: QuotaConfig = {
  requestsPerMinute: 60,
  requestsPerDay: 1500,
  tokensPerRequest: 32768
};

export class QuotaManager {
  private requestCount = 0;
  private lastReset = Date.now();
  
  async checkQuota(): Promise<boolean> {
    const now = Date.now();
    const minutesPassed = (now - this.lastReset) / (1000 * 60);
    
    if (minutesPassed >= 1) {
      this.requestCount = 0;
      this.lastReset = now;
    }
    
    return this.requestCount < QUOTA_LIMITS.requestsPerMinute;
  }
}
```

## 分析服务集成

### 1. PostHog 集成

#### 配置
```typescript
// lib/analytics/posthog.ts
import { PostHog } from 'posthog-node';

const posthog = new PostHog(
  process.env.NEXT_PUBLIC_POSTHOG_KEY!,
  {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://app.posthog.com',
    flushAt: 20,
    flushInterval: 10000
  }
);

export { posthog };
```

#### 事件追踪
```typescript
// lib/analytics/events.ts
export const trackEvent = (
  userId: string,
  event: string,
  properties?: Record<string, any>
) => {
  if (typeof window !== 'undefined' && posthog) {
    posthog.capture(event, {
      userId,
      ...properties,
      timestamp: new Date().toISOString()
    });
  }
};

// 使用示例
trackEvent('user-123', 'page_view', {
  page: '/products',
  locale: 'zh'
});
```

### 2. Google Analytics 集成

#### 配置
```typescript
// lib/analytics/gtag.ts
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID;

export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', GA_TRACKING_ID!, {
      page_path: url,
    });
  }
};

export const event = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};
```

#### 页面追踪
```typescript
// app/[locale]/layout.tsx
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { pageview } from '@/lib/analytics/gtag';

export default function Layout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'production') {
      pageview(pathname);
    }
  }, [pathname]);
  
  return <>{children}</>;
}
```

### 3. Hotjar 集成

#### 配置
```typescript
// lib/analytics/hotjar.ts
export const HOTJAR_ID = process.env.NEXT_PUBLIC_HOTJAR_ID;

export const initHotjar = () => {
  if (typeof window !== 'undefined' && HOTJAR_ID) {
    (function(h: any, o: any, t: any, j: any, a?: any, r?: any) {
      h.hj = h.hj || function(...args: any[]) {
        (h.hj.q = h.hj.q || []).push(args);
      };
      h._hjSettings = { hjid: HOTJAR_ID, hjsv: 6 };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');
  }
};
```

## 内部API设计

### 1. API路由结构
```
app/api/
├── auth/              # 认证相关API
├── i18n/              # 国际化API
├── analytics/         # 分析数据API
└── health/            # 健康检查API
```

### 2. API响应格式

#### 标准响应结构
```typescript
// types/api.ts
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

// 成功响应
const successResponse = <T>(data: T): ApiResponse<T> => ({
  success: true,
  data,
  meta: {
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
    version: '1.0'
  }
});

// 错误响应
const errorResponse = (code: string, message: string): ApiResponse => ({
  success: false,
  error: { code, message },
  meta: {
    timestamp: new Date().toISOString(),
    requestId: crypto.randomUUID(),
    version: '1.0'
  }
});
```

### 3. API示例

#### 翻译API
```typescript
// app/api/i18n/translate/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { translateText } from '@/lib/gemini/client';

export async function POST(request: NextRequest) {
  try {
    const { text, targetLanguage } = await request.json();
    
    if (!text || !targetLanguage) {
      return NextResponse.json(
        errorResponse('INVALID_INPUT', 'Missing required fields'),
        { status: 400 }
      );
    }
    
    const translatedText = await translateText(text, targetLanguage);
    
    return NextResponse.json(
      successResponse({ translatedText })
    );
  } catch (error) {
    return NextResponse.json(
      errorResponse('TRANSLATION_ERROR', 'Translation failed'),
      { status: 500 }
    );
  }
}
```

## 中间件配置

### 1. 安全中间件
```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { securityMiddleware } from '@/lib/security/middleware';

export function middleware(request: NextRequest) {
  // 1. 安全检查
  const securityResponse = securityMiddleware(request);
  if (securityResponse) return securityResponse;
  
  // 2. 国际化处理
  const i18nResponse = handleI18n(request);
  if (i18nResponse) return i18nResponse;
  
  // 3. 分析追踪
  const response = NextResponse.next();
  addAnalyticsHeaders(response, request);
  
  return response;
}
```

### 2. CORS配置
```typescript
// lib/security/cors.ts
export const CORS_CONFIG = {
  origin: process.env.NODE_ENV === 'production' 
    ? ['https://yourdomain.com']
    : ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
};
```

## 错误处理

### 1. 全局错误处理
```typescript
// lib/errors/handler.ts
export class ApiError extends Error {
  constructor(
    public code: string,
    public message: string,
    public statusCode: number = 500,
    public details?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const handleApiError = (error: unknown): ApiResponse => {
  if (error instanceof ApiError) {
    return errorResponse(error.code, error.message);
  }
  
  console.error('Unexpected error:', error);
  return errorResponse('INTERNAL_ERROR', 'Internal server error');
};
```

### 2. 重试机制
```typescript
// lib/utils/retry.ts
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;
      
      if (i === maxRetries) break;
      
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
  
  throw lastError!;
}
```

## 安全配置

### 1. API密钥管理
```typescript
// lib/security/keys.ts
export const validateApiKey = (key: string): boolean => {
  // Google API Key 格式验证
  if (key.startsWith('AIzaSy') && key.length === 39) {
    return true;
  }
  
  return false;
};

export const maskApiKey = (key: string): string => {
  if (key.length < 8) return '***';
  return key.slice(0, 4) + '***' + key.slice(-4);
};
```

### 2. 请求验证
```typescript
// lib/security/validation.ts
import { z } from 'zod';

export const translateRequestSchema = z.object({
  text: z.string().min(1).max(10000),
  targetLanguage: z.enum(['zh', 'en', 'ja', 'ko']),
  preserveFormatting: z.boolean().optional().default(true)
});

export const validateRequest = <T>(
  schema: z.ZodSchema<T>,
  data: unknown
): T => {
  try {
    return schema.parse(data);
  } catch (error) {
    throw new ApiError('VALIDATION_ERROR', 'Invalid request data', 400, error);
  }
};
```

## 监控和日志

### 1. API监控
```typescript
// lib/monitoring/metrics.ts
export const trackApiCall = (
  endpoint: string,
  method: string,
  statusCode: number,
  duration: number
) => {
  // 发送到监控服务
  if (process.env.NODE_ENV === 'production') {
    console.log(JSON.stringify({
      type: 'api_call',
      endpoint,
      method,
      statusCode,
      duration,
      timestamp: new Date().toISOString()
    }));
  }
};
```

### 2. 错误日志
```typescript
// lib/monitoring/logger.ts
export const logError = (
  error: Error,
  context: Record<string, any> = {}
) => {
  const logEntry = {
    level: 'error',
    message: error.message,
    stack: error.stack,
    context,
    timestamp: new Date().toISOString()
  };
  
  console.error(JSON.stringify(logEntry));
  
  // 在生产环境中发送到错误追踪服务
  if (process.env.NODE_ENV === 'production') {
    // 发送到 Sentry 或其他错误追踪服务
  }
};
```

## 相关文档

### 基础文档
- [项目架构文档](./项目架构文档.md) - 了解整体架构设计
- [开发环境搭建指南](./开发环境搭建指南.md) - 了解环境配置

### 运维文档
- [部署运维指南](./部署运维指南.md) - 了解部署和监控
- [翻译工作流程](./翻译工作流程.md) - 了解翻译API使用

---

**文档维护**：本文档由后端团队维护，如有问题请联系API负责人。
**最后更新**：2024年1月
