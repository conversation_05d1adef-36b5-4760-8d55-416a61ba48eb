import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { errorLogger, logError, logWarning, logInfo } from '@/lib/error/error-logger';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock fetch
const fetchMock = vi.fn();

// Mock console methods
const consoleMock = {
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn(),
};

describe('Error Logger', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock global objects
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
    
    global.fetch = fetchMock;
    
    Object.defineProperty(console, 'error', { value: consoleMock.error });
    Object.defineProperty(console, 'warn', { value: consoleMock.warn });
    Object.defineProperty(console, 'log', { value: consoleMock.log });
    
    // Mock navigator
    Object.defineProperty(window, 'navigator', {
      value: { userAgent: 'test-user-agent' },
      writable: true,
    });
    
    // Mock location
    Object.defineProperty(window, 'location', {
      value: { href: 'http://localhost:3000/test' },
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Basic Error Logging', () => {
    it('should log error with basic information', () => {
      errorLogger.logError({
        message: 'Test error message',
        stack: 'Error stack trace',
        context: { testKey: 'testValue' },
      });

      expect(consoleMock.error).toHaveBeenCalledWith(
        expect.stringContaining('Test error message'),
        expect.objectContaining({
          id: expect.stringMatching(/^error_\d+_[a-z0-9]+$/),
          stack: 'Error stack trace',
          context: { testKey: 'testValue' },
        })
      );
    });

    it('should log warning with correct level', () => {
      errorLogger.logError({
        message: 'Test warning message',
        level: 'warn',
      });

      expect(consoleMock.warn).toHaveBeenCalledWith(
        expect.stringContaining('Test warning message'),
        expect.any(Object)
      );
    });

    it('should log info with correct level', () => {
      errorLogger.logError({
        message: 'Test info message',
        level: 'info',
      });

      expect(consoleMock.log).toHaveBeenCalledWith(
        expect.stringContaining('Test info message'),
        expect.any(Object)
      );
    });
  });

  describe('Local Storage Logging', () => {
    it('should save error to localStorage', () => {
      localStorageMock.getItem.mockReturnValue('[]');
      
      errorLogger.logError({
        message: 'Test localStorage error',
      });

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'error_logs',
        expect.stringContaining('Test localStorage error')
      );
    });

    it('should limit localStorage entries', () => {
      const existingLogs = Array(100).fill(null).map((_, i) => ({
        id: `error_${i}`,
        message: `Error ${i}`,
        timestamp: new Date().toISOString(),
        level: 'error',
      }));
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(existingLogs));
      
      errorLogger.logError({
        message: 'New error',
      });

      const setItemCall = localStorageMock.setItem.mock.calls[0];
      const savedLogs = JSON.parse(setItemCall[1]);
      expect(savedLogs.length).toBeLessThanOrEqual(50); // maxLocalStorageEntries
    });

    it('should handle localStorage errors gracefully', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      expect(() => {
        errorLogger.logError({
          message: 'Test error',
        });
      }).not.toThrow();

      expect(consoleMock.warn).toHaveBeenCalledWith(
        'Failed to save error log to localStorage:',
        expect.any(Error)
      );
    });
  });

  describe('React Error Logging', () => {
    it('should log React errors with component stack', () => {
      const error = new Error('React component error');
      const errorInfo = {
        componentStack: 'Component stack trace',
      };

      errorLogger.logReactError(error, errorInfo);

      expect(consoleMock.error).toHaveBeenCalledWith(
        expect.stringContaining('React Error: React component error'),
        expect.objectContaining({
          context: expect.objectContaining({
            type: 'react_error',
            componentStack: 'Component stack trace',
            errorBoundary: true,
          }),
        })
      );
    });
  });

  describe('API Error Logging', () => {
    it('should log API errors with request details', () => {
      errorLogger.logApiError({
        url: '/api/test',
        method: 'POST',
        status: 500,
        message: 'Internal Server Error',
        response: { error: 'Server error' },
      });

      expect(consoleMock.error).toHaveBeenCalledWith(
        expect.stringContaining('API Error: Internal Server Error'),
        expect.objectContaining({
          context: expect.objectContaining({
            type: 'api_error',
            url: '/api/test',
            method: 'POST',
            status: 500,
            response: { error: 'Server error' },
          }),
        })
      );
    });
  });

  describe('Remote Logging', () => {
    it('should send error to remote endpoint when configured', async () => {
      fetchMock.mockResolvedValue(new Response('OK'));
      
      errorLogger.updateConfig({
        enableRemoteLogging: true,
        remoteEndpoint: 'https://api.example.com/errors',
        apiKey: 'test-api-key',
      });

      errorLogger.logError({
        message: 'Remote error test',
      });

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(fetchMock).toHaveBeenCalledWith(
        'https://api.example.com/errors',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
          }),
          body: expect.stringContaining('Remote error test'),
        })
      );
    });

    it('should handle remote logging errors gracefully', async () => {
      fetchMock.mockRejectedValue(new Error('Network error'));
      
      errorLogger.updateConfig({
        enableRemoteLogging: true,
        remoteEndpoint: 'https://api.example.com/errors',
      });

      errorLogger.logError({
        message: 'Remote error test',
      });

      // Wait for async operation
      await new Promise(resolve => setTimeout(resolve, 0));

      expect(consoleMock.warn).toHaveBeenCalledWith(
        'Failed to send error log to remote endpoint:',
        expect.any(Error)
      );
    });
  });

  describe('Local Logs Management', () => {
    it('should retrieve local logs', () => {
      const mockLogs = [
        { id: 'error_1', message: 'Error 1', timestamp: '2023-01-01T00:00:00.000Z', level: 'error' },
        { id: 'error_2', message: 'Error 2', timestamp: '2023-01-01T00:01:00.000Z', level: 'warn' },
      ];
      
      localStorageMock.getItem.mockReturnValue(JSON.stringify(mockLogs));
      
      const logs = errorLogger.getLocalLogs();
      
      expect(logs).toEqual(mockLogs);
      expect(localStorageMock.getItem).toHaveBeenCalledWith('error_logs');
    });

    it('should return empty array when no logs exist', () => {
      localStorageMock.getItem.mockReturnValue(null);
      
      const logs = errorLogger.getLocalLogs();
      
      expect(logs).toEqual([]);
    });

    it('should clear local logs', () => {
      errorLogger.clearLocalLogs();
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('error_logs');
    });

    it('should handle localStorage retrieval errors', () => {
      localStorageMock.getItem.mockImplementation(() => {
        throw new Error('localStorage error');
      });
      
      const logs = errorLogger.getLocalLogs();
      
      expect(logs).toEqual([]);
      expect(consoleMock.warn).toHaveBeenCalledWith(
        'Failed to retrieve error logs from localStorage:',
        expect.any(Error)
      );
    });
  });

  describe('Convenience Functions', () => {
    it('should use logError convenience function', () => {
      logError('Convenience error test', { key: 'value' });
      
      expect(consoleMock.error).toHaveBeenCalledWith(
        expect.stringContaining('Convenience error test'),
        expect.objectContaining({
          context: { key: 'value' },
        })
      );
    });

    it('should use logWarning convenience function', () => {
      logWarning('Convenience warning test', { key: 'value' });
      
      expect(consoleMock.warn).toHaveBeenCalledWith(
        expect.stringContaining('Convenience warning test'),
        expect.objectContaining({
          context: { key: 'value' },
        })
      );
    });

    it('should use logInfo convenience function', () => {
      logInfo('Convenience info test', { key: 'value' });
      
      expect(consoleMock.log).toHaveBeenCalledWith(
        expect.stringContaining('Convenience info test'),
        expect.objectContaining({
          context: { key: 'value' },
        })
      );
    });
  });

  describe('Configuration Updates', () => {
    it('should update configuration', () => {
      errorLogger.updateConfig({
        enableConsoleLog: false,
        maxLocalStorageEntries: 200,
      });

      // Test that console logging is disabled
      errorLogger.logError({
        message: 'Test after config update',
      });

      expect(consoleMock.error).not.toHaveBeenCalled();
    });
  });
});
