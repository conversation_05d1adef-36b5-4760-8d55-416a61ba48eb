# 部署运维指南

## 目录
- [概述](#概述)
- [部署环境](#部署环境)
- [CI/CD流程](#cicd流程)
- [环境配置](#环境配置)
- [部署步骤](#部署步骤)
- [监控告警](#监控告警)
- [备份策略](#备份策略)
- [故障处理](#故障处理)

## 概述

本项目采用现代化的部署策略，支持多环境部署和自动化CI/CD流程。主要部署平台为Vercel，同时支持其他云平台部署。

### 部署架构
- **开发环境**：本地开发和测试
- **预览环境**：功能分支自动部署
- **生产环境**：主分支自动部署
- **CDN**：全球内容分发网络

## 部署环境

### 1. 环境分类

| 环境 | 分支 | 域名 | 用途 |
|------|------|------|------|
| 开发环境 | feature/* | localhost:3000 | 本地开发测试 |
| 预览环境 | feature/* | preview-*.vercel.app | 功能预览和测试 |
| 测试环境 | develop | staging.yourdomain.com | 集成测试 |
| 生产环境 | main | yourdomain.com | 正式发布 |

### 2. 平台支持

#### Vercel（推荐）
```bash
# 优势
- 零配置部署
- 自动HTTPS
- 全球CDN
- 分支预览
- 性能监控

# 适用场景
- 静态站点
- Next.js应用
- 快速原型
```

#### 其他平台
```bash
# Netlify
- 静态站点托管
- 表单处理
- 函数计算

# AWS
- 完整云服务
- 高度可定制
- 企业级支持

# Docker
- 容器化部署
- 多云支持
- 本地一致性
```

## CI/CD流程

### 1. GitHub Actions工作流

#### 主工作流 (.github/workflows/deploy.yml)
```yaml
name: 部署流程
on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: 安装依赖
        run: npm ci
      
      - name: 运行测试
        run: npm run test
      
      - name: 类型检查
        run: npm run typecheck
      
      - name: 代码检查
        run: npm run lint
      
      - name: 构建测试
        run: npm run build

  deploy-preview:
    needs: test
    if: github.event_name == 'pull_request'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}

  deploy-production:
    needs: test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
```

#### 翻译工作流 (.github/workflows/i18n.yml)
```yaml
name: 自动翻译
on:
  push:
    paths:
      - 'content/en/**'
      - '*.md'
  workflow_dispatch:

jobs:
  translate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: 安装依赖
        run: npm ci
      
      - name: 运行翻译
        env:
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
        run: npm run i18n:ci
      
      - name: 创建PR
        if: success()
        uses: peter-evans/create-pull-request@v5
        with:
          title: '🌍 自动翻译更新'
          body: '自动生成的翻译内容更新'
          branch: auto-translation
```

### 2. 部署检查

#### 预部署检查脚本
```typescript
// scripts/pre-deploy-check.ts
import { execSync } from 'child_process';

const checks = [
  {
    name: '环境变量检查',
    command: 'npm run config:check'
  },
  {
    name: '类型检查',
    command: 'npm run typecheck'
  },
  {
    name: '代码质量检查',
    command: 'npm run lint'
  },
  {
    name: '测试运行',
    command: 'npm run test'
  },
  {
    name: '构建测试',
    command: 'npm run build'
  }
];

async function runPreDeployChecks() {
  console.log('🚀 开始预部署检查...\n');
  
  for (const check of checks) {
    try {
      console.log(`⏳ ${check.name}...`);
      execSync(check.command, { stdio: 'pipe' });
      console.log(`✅ ${check.name} 通过\n`);
    } catch (error) {
      console.error(`❌ ${check.name} 失败`);
      console.error(error);
      process.exit(1);
    }
  }
  
  console.log('🎉 所有检查通过，可以部署！');
}

runPreDeployChecks();
```

## 环境配置

### 1. Vercel配置

#### vercel.json
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": ".next",
  "framework": "nextjs",
  "installCommand": "npm ci",
  "devCommand": "npm run dev",
  "env": {
    "GOOGLE_API_KEY": "@google-api-key",
    "NEXT_PUBLIC_POSTHOG_KEY": "@posthog-key",
    "NEXT_PUBLIC_GA_ID": "@ga-id"
  },
  "build": {
    "env": {
      "GOOGLE_API_KEY": "@google-api-key"
    }
  },
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/",
      "destination": "/en",
      "permanent": false
    }
  ]
}
```

### 2. 环境变量管理

#### 生产环境变量
```bash
# 必需变量
GOOGLE_API_KEY=AIzaSy...
NEXT_PUBLIC_APP_URL=https://yourdomain.com

# 分析工具
NEXT_PUBLIC_POSTHOG_KEY=phc_...
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
NEXT_PUBLIC_GA_ID=G-...

# 安全配置
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=https://yourdomain.com
```

#### 环境变量验证
```typescript
// lib/env.ts
import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  server: {
    GOOGLE_API_KEY: z.string().min(1),
    NEXTAUTH_SECRET: z.string().min(1),
  },
  client: {
    NEXT_PUBLIC_APP_URL: z.string().url(),
    NEXT_PUBLIC_POSTHOG_KEY: z.string().optional(),
    NEXT_PUBLIC_GA_ID: z.string().optional(),
  },
  runtimeEnv: {
    GOOGLE_API_KEY: process.env.GOOGLE_API_KEY,
    NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_GA_ID: process.env.NEXT_PUBLIC_GA_ID,
  },
});
```

## 部署步骤

### 1. 首次部署

#### Vercel部署
```bash
# 1. 安装Vercel CLI
npm i -g vercel

# 2. 登录Vercel
vercel login

# 3. 初始化项目
vercel

# 4. 配置环境变量
vercel env add GOOGLE_API_KEY production
vercel env add NEXT_PUBLIC_APP_URL production

# 5. 部署
vercel --prod
```

#### 手动部署
```bash
# 1. 构建项目
npm run build

# 2. 导出静态文件（如需要）
npm run export

# 3. 上传到服务器
rsync -avz .next/ user@server:/var/www/app/

# 4. 重启服务
ssh user@server "pm2 restart app"
```

### 2. 日常部署

#### 自动部署（推荐）
```bash
# 1. 提交代码到主分支
git push origin main

# 2. GitHub Actions自动触发部署
# 3. 检查部署状态
vercel --prod --confirm
```

#### 手动部署
```bash
# 1. 运行预部署检查
npm run pre-deploy-check

# 2. 部署到生产环境
vercel --prod

# 3. 验证部署
curl -I https://yourdomain.com
```

### 3. 回滚策略

#### Vercel回滚
```bash
# 查看部署历史
vercel ls

# 回滚到指定版本
vercel rollback [deployment-url]

# 或通过Vercel Dashboard回滚
```

#### Git回滚
```bash
# 回滚到上一个提交
git revert HEAD

# 回滚到指定提交
git revert [commit-hash]

# 强制回滚（谨慎使用）
git reset --hard [commit-hash]
git push --force-with-lease origin main
```

## 监控告警

### 1. 性能监控

#### Vercel Analytics
```typescript
// app/layout.tsx
import { Analytics } from '@vercel/analytics/react';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
```

#### 自定义监控
```typescript
// lib/monitoring/performance.ts
export const trackPerformance = () => {
  if (typeof window !== 'undefined') {
    // Core Web Vitals
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(console.log);
      getFID(console.log);
      getFCP(console.log);
      getLCP(console.log);
      getTTFB(console.log);
    });
  }
};
```

### 2. 错误监控

#### 错误边界
```typescript
// components/error-boundary.tsx
'use client';

import { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Error caught by boundary:', error, errorInfo);
    
    // 发送错误到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 发送到Sentry或其他错误追踪服务
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="error-fallback">
          <h2>出现了错误</h2>
          <p>请刷新页面重试</p>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### 3. 健康检查

#### 健康检查API
```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';

export async function GET() {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    checks: {
      database: await checkDatabase(),
      external_apis: await checkExternalAPIs(),
      memory: process.memoryUsage(),
    }
  };

  const isHealthy = Object.values(health.checks).every(
    check => typeof check === 'object' ? check.status === 'ok' : true
  );

  return NextResponse.json(health, {
    status: isHealthy ? 200 : 503
  });
}

async function checkDatabase(): Promise<{ status: string }> {
  // 数据库连接检查
  return { status: 'ok' };
}

async function checkExternalAPIs(): Promise<{ status: string }> {
  // 外部API检查
  try {
    // 检查Google API
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models', {
      headers: { 'Authorization': `Bearer ${process.env.GOOGLE_API_KEY}` }
    });
    
    return { status: response.ok ? 'ok' : 'error' };
  } catch {
    return { status: 'error' };
  }
}
```

## 备份策略

### 1. 代码备份
```bash
# Git仓库备份
git clone --mirror https://github.com/your-org/repo.git
git remote set-url --push origin https://backup-git-server.com/repo.git

# 定期推送备份
git push --mirror
```

### 2. 配置备份
```bash
# 环境变量备份
vercel env ls > env-backup.txt

# 配置文件备份
tar -czf config-backup.tar.gz \
  vercel.json \
  next.config.ts \
  package.json \
  .env.example
```

### 3. 内容备份
```bash
# 内容文件备份
tar -czf content-backup.tar.gz content/

# 自动化备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "backup_${DATE}.tar.gz" content/ docs/
aws s3 cp "backup_${DATE}.tar.gz" s3://your-backup-bucket/
```

## 故障处理

### 1. 常见问题

#### 构建失败
```bash
# 检查构建日志
vercel logs [deployment-url]

# 本地复现
npm run build

# 清理缓存
rm -rf .next node_modules
npm install
npm run build
```

#### 环境变量问题
```bash
# 检查环境变量
vercel env ls

# 更新环境变量
vercel env rm VARIABLE_NAME production
vercel env add VARIABLE_NAME production
```

#### 性能问题
```bash
# 分析构建包大小
npm run build -- --analyze

# 检查Core Web Vitals
# 使用Lighthouse或PageSpeed Insights
```

### 2. 紧急响应

#### 快速回滚
```bash
# 1. 立即回滚到上一个版本
vercel rollback

# 2. 通知团队
# 3. 分析问题原因
# 4. 修复并重新部署
```

#### 服务降级
```bash
# 1. 启用维护模式
# 2. 禁用非关键功能
# 3. 减少外部API调用
# 4. 启用缓存
```

## 相关文档

### 基础文档
- [项目架构文档](./项目架构文档.md) - 了解项目架构和技术栈
- [开发环境搭建指南](./开发环境搭建指南.md) - 了解开发环境配置

### 集成文档
- [API集成文档](./API集成文档.md) - 了解外部服务配置
- [翻译工作流程](./翻译工作流程.md) - 了解翻译自动化部署

---

**文档维护**：本文档由DevOps团队维护，如有问题请联系运维负责人。
**最后更新**：2024年1月
