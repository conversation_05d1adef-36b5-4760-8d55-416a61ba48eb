import { describe, it, expect, vi, beforeEach } from 'vitest';
import { redirect } from 'next/navigation';
import { headers } from 'next/headers';
import RootPage from '@/app/page';

// Mock Next.js functions
vi.mock('next/navigation', () => ({
  redirect: vi.fn(),
}));

vi.mock('next/headers', () => ({
  headers: vi.fn(),
}));

describe('Root Page', () => {
  const mockRedirect = vi.mocked(redirect);
  const mockHeaders = vi.mocked(headers);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Language Detection and Redirect', () => {
    it('should redirect to Chinese locale when accept-language contains zh', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('zh-CN,zh;q=0.9,en;q=0.8'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockHeaders).toHaveBeenCalled();
      expect(mockHeadersList.get).toHaveBeenCalledWith('accept-language');
      expect(mockRedirect).toHaveBeenCalledWith('/zh');
    });

    it('should redirect to English locale when accept-language contains zh-TW', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('zh-TW,zh;q=0.9,en;q=0.8'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockRedirect).toHaveBeenCalledWith('/zh');
    });

    it('should redirect to English locale when accept-language does not contain zh', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('en-US,en;q=0.9,fr;q=0.8'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should redirect to English locale when accept-language is empty', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue(''),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should redirect to English locale when accept-language header is null', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue(null),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should handle various Chinese language codes', async () => {
      const chineseLanguageCodes = [
        'zh',
        'zh-CN',
        'zh-TW',
        'zh-HK',
        'zh-SG',
        'zh-Hans',
        'zh-Hant',
      ];

      for (const langCode of chineseLanguageCodes) {
        const mockHeadersList = {
          get: vi.fn().mockReturnValue(`${langCode},en;q=0.8`),
        };
        mockHeaders.mockResolvedValue(mockHeadersList as any);

        await RootPage();

        expect(mockRedirect).toHaveBeenCalledWith('/zh');
        
        // Reset mocks for next iteration
        vi.clearAllMocks();
      }
    });

    it('should handle complex accept-language headers', async () => {
      const complexHeaders = [
        'zh-CN,zh;q=0.9,en;q=0.8,ja;q=0.7',
        'en-US,en;q=0.9,zh;q=0.8,fr;q=0.7',
        'fr-FR,fr;q=0.9,en;q=0.8,zh-CN;q=0.7',
        'ja-JP,ja;q=0.9,en;q=0.8',
      ];

      const expectedRedirects = ['/zh', '/zh', '/zh', '/en'];

      for (let i = 0; i < complexHeaders.length; i++) {
        const mockHeadersList = {
          get: vi.fn().mockReturnValue(complexHeaders[i]),
        };
        mockHeaders.mockResolvedValue(mockHeadersList as any);

        await RootPage();

        expect(mockRedirect).toHaveBeenCalledWith(expectedRedirects[i]);
        
        // Reset mocks for next iteration
        vi.clearAllMocks();
      }
    });

    it('should handle case-insensitive language detection', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('ZH-CN,ZH;q=0.9,EN;q=0.8'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      expect(mockRedirect).toHaveBeenCalledWith('/zh');
    });

    it('should handle partial matches correctly', async () => {
      // Should not match 'zh' in 'czech' or other non-Chinese languages
      const nonChineseHeaders = [
        'cs-CZ,cs;q=0.9,en;q=0.8', // Czech
        'de-DE,de;q=0.9,en;q=0.8', // German
        'ar-SA,ar;q=0.9,en;q=0.8', // Arabic
      ];

      for (const header of nonChineseHeaders) {
        const mockHeadersList = {
          get: vi.fn().mockReturnValue(header),
        };
        mockHeaders.mockResolvedValue(mockHeadersList as any);

        await RootPage();

        expect(mockRedirect).toHaveBeenCalledWith('/en');
        
        // Reset mocks for next iteration
        vi.clearAllMocks();
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle headers() throwing an error', async () => {
      mockHeaders.mockRejectedValue(new Error('Headers error'));

      // Should not throw, but we can't easily test the redirect in this case
      // since the error would be thrown before we reach the redirect
      await expect(RootPage()).rejects.toThrow('Headers error');
    });

    it('should handle redirect throwing an error', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('en-US'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);
      mockRedirect.mockImplementation(() => {
        throw new Error('Redirect error');
      });

      await expect(RootPage()).rejects.toThrow('Redirect error');
    });
  });

  describe('Integration', () => {
    it('should call headers and redirect in correct order', async () => {
      const mockHeadersList = {
        get: vi.fn().mockReturnValue('en-US'),
      };
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await RootPage();

      // Verify call order
      expect(mockHeaders).toHaveBeenCalledBefore(mockRedirect as any);
      expect(mockHeadersList.get).toHaveBeenCalledWith('accept-language');
      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should work with real-world accept-language examples', async () => {
      const realWorldExamples = [
        {
          header: 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
          expected: '/zh',
          description: 'Chinese user with English fallbacks',
        },
        {
          header: 'en-US,en;q=0.9,es;q=0.8,fr;q=0.7',
          expected: '/en',
          description: 'English user with Spanish and French',
        },
        {
          header: 'ja-JP,ja;q=0.9,en;q=0.8,zh;q=0.7',
          expected: '/zh',
          description: 'Japanese user with Chinese preference',
        },
        {
          header: 'fr-FR,fr;q=0.9,en;q=0.8',
          expected: '/en',
          description: 'French user with English fallback',
        },
      ];

      for (const example of realWorldExamples) {
        const mockHeadersList = {
          get: vi.fn().mockReturnValue(example.header),
        };
        mockHeaders.mockResolvedValue(mockHeadersList as any);

        await RootPage();

        expect(mockRedirect).toHaveBeenCalledWith(example.expected);
        
        // Reset mocks for next iteration
        vi.clearAllMocks();
      }
    });
  });
});
