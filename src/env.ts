import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  server: {
    // 邮件服务（可选）
    RESEND_TOKEN: z.string().startsWith('re_').optional(),
    RESEND_FROM: z.string().email().optional(),

    // 安全防护（可选）
    ARCJET_KEY: z.string().startsWith('ajkey_').optional(),

    // 安全配置
    SECURITY_SECRET: z.string().min(32).optional(),
    CSRF_SECRET: z.string().min(32).optional(),

    // Redis 缓存（可选）
    UPSTASH_REDIS_REST_URL: z.string().url().optional(),
    UPSTASH_REDIS_REST_TOKEN: z.string().optional(),

    // 文件存储（可选）
    BLOB_READ_WRITE_TOKEN: z.string().optional(),
  },

  client: {
    // 基础配置（必需）
    NEXT_PUBLIC_APP_URL: z.string().url().default('http://localhost:3000'),

    // 分析工具（三选一或组合使用）
    NEXT_PUBLIC_POSTHOG_KEY: z.string().startsWith('phc_').optional(),
    NEXT_PUBLIC_POSTHOG_HOST: z.string().url().optional(),
    NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().startsWith('G-').optional(),

    // 监控服务（可选）
    NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),
  },

  runtimeEnv: {
    // 服务端变量
    RESEND_TOKEN: process.env.RESEND_TOKEN,
    RESEND_FROM: process.env.RESEND_FROM,
    ARCJET_KEY: process.env.ARCJET_KEY,
    SECURITY_SECRET: process.env.SECURITY_SECRET,
    CSRF_SECRET: process.env.CSRF_SECRET,
    UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
    UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,
    BLOB_READ_WRITE_TOKEN: process.env.BLOB_READ_WRITE_TOKEN,

    // 客户端变量
    NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,
    NEXT_PUBLIC_POSTHOG_KEY: process.env.NEXT_PUBLIC_POSTHOG_KEY,
    NEXT_PUBLIC_POSTHOG_HOST: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
    NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,
  },

  skipValidation: !!process.env.SKIP_ENV_VALIDATION,
});
