/**
 * 交互组件包装器导出
 *
 * 这个模块提供了所有需要客户端交互的组件包装器，
 * 用于在服务器组件中安全地使用交互功能。
 */

// 基础交互按钮组件
export {
  InteractiveButton,
  NavigationButton as InteractiveNavigationButton,
  ContactButton as InteractiveContactButton,
  ExternalLinkButton as InteractiveExternalLinkButton,
} from './interactive-button';

// 专门的联系交互组件
export { EmailButton, PhoneButton, ContactButton } from './contact-button';

// 专门的导航交互组件
export {
  NavigationButton,
  ExternalLinkButton,
  LocalizedNavigationButton,
  LinkButton,
} from './navigation-button';

// 专门的表单交互组件
export {
  FormSubmitButton,
  ContactFormSubmitButton,
  SimpleSubmitButton,
} from './form-submit-button';

// 专门的返回按钮组件
export { BackButton } from './back-button';

// 未来可以添加更多交互组件包装器
// export { InteractiveForm } from './interactive-form'
// export { InteractiveDropdown } from './interactive-dropdown'
// export { InteractiveModal } from './interactive-modal'
