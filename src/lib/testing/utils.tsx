import { render, RenderOptions, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactElement } from 'react';

// 创建测试用的 QueryClient
function createTestQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
}

// 自定义渲染函数
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = createTestQueryClient(), ...renderOptions } = options;

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    );
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    queryClient,
  };
}

/**
 * 异步组件渲染工具函数
 * 专门用于处理 React 19 异步组件的测试渲染
 *
 * 注意：这个函数现在主要用于渲染使用了异步组件包装器的组件
 */
export async function renderAsyncComponent(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let result: any;

  await act(async () => {
    result = renderWithProviders(ui, options);
  });

  return result;
}

/**
 * 异步组件渲染工具函数（简化版）
 * 直接使用 render 而不包装 providers
 */
export async function renderAsync(
  ui: ReactElement,
  options: RenderOptions = {}
) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let result: any;

  await act(async () => {
    result = render(ui, options);
  });

  return result;
}

/**
 * 等待异步组件加载完成
 * 用于等待异步组件包装器完成Promise解析
 */
export async function waitForAsyncComponent() {
  // 等待一个微任务周期，确保Promise解析完成
  await act(async () => {
    await new Promise((resolve) => setTimeout(resolve, 0));
  });
}

// 重新导出所有 testing-library 工具
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
