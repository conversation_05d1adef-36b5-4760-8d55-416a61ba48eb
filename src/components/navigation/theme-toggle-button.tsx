'use client';

import { Button } from '@/components/ui/button';
import { useAppStore } from '@/stores/app-store';

interface ThemeToggleButtonProps {
  toggleText: string;
}

export function ThemeToggleButton({ toggleText }: ThemeToggleButtonProps) {
  const { theme, setTheme } = useAppStore();

  const handleToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <Button variant="outline" size="lg" onClick={handleToggle}>
      {toggleText} ({theme})
    </Button>
  );
}
