import { type Metadata } from 'next';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  LocalizedNavigationButton,
  ContactButton,
} from '@/components/interactive';
import { type Locale } from '@/lib/i18n/config';
import {
  Droplets,
  Shield,
  Award,
  ArrowRight,
  Star,
  CheckCircle,
} from 'lucide-react';

interface ProductsPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({
  params,
}: ProductsPageProps): Promise<Metadata> {
  const { locale } = await params;

  const metadata = {
    en: {
      title: 'Flood Protection Solutions | Professional Water Barriers',
      description:
        'Professional flood protection products including water absorbing bags, ABS modular barriers, and aluminum custom water barriers. Maximum safety and reliability.',
      keywords: [
        'flood protection',
        'water barriers',
        'emergency protection',
        'flood control',
        'water absorbing bags',
      ],
    },
    zh: {
      title: '防洪保护解决方案 | 专业挡水板',
      description:
        '专业防洪保护产品，包括吸水膨胀袋、ABS组合式防洪板和铝合金定制挡水板。最大安全性和可靠性。',
      keywords: ['防洪保护', '挡水板', '应急保护', '防洪控制', '吸水膨胀袋'],
    },
  };

  const currentMetadata = metadata[locale] || metadata.en;

  return {
    title: currentMetadata.title,
    description: currentMetadata.description,
    keywords: currentMetadata.keywords,
    openGraph: {
      title: currentMetadata.title,
      description: currentMetadata.description,
      type: 'website',
    },
  };
}

export default async function ProductsPage({ params }: ProductsPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'Flood Protection Solutions',
      subtitle:
        'Professional flood protection products designed for maximum safety and reliability',
      viewDetails: 'View Details',
      learnMore: 'Learn More',
      features: 'Key Features',
      products: [
        {
          title: 'Water Absorbing Bags',
          description:
            'Rapid deployment flood protection with instant water absorption technology',
          category: 'Emergency Protection',
          href: '/products/water-absorbing-bags',
          icon: Droplets,
          color: 'blue',
          features: [
            'Rapid 3-5 minute activation',
            'Expands 20x original size',
            'Lightweight and portable',
            'Environmentally friendly',
            '5-year shelf life',
          ],
          popular: true,
        },
        {
          title: 'ABS Modular Flood Barriers',
          description:
            'Professional modular flood protection system with superior durability',
          category: 'Permanent Protection',
          href: '/products/abs-flood-barriers',
          icon: Shield,
          color: 'slate',
          features: [
            'Modular interlocking design',
            'High-strength ABS construction',
            'UV and weather resistant',
            'Tool-free assembly',
            '20+ year lifespan',
          ],
          popular: false,
        },
        {
          title: 'Aluminum Custom Water Barriers',
          description:
            'Premium custom-engineered aluminum flood barriers for critical applications',
          category: 'Premium Solutions',
          href: '/products/aluminum-water-barriers',
          icon: Award,
          color: 'amber',
          features: [
            'Marine-grade aluminum',
            'Custom engineering',
            'Corrosion resistant',
            'Aesthetic integration',
            '25-year warranty',
          ],
          popular: false,
        },
      ],
    },
    zh: {
      title: '防洪保护解决方案',
      subtitle: '专业防洪保护产品，专为最大安全性和可靠性而设计',
      viewDetails: '查看详情',
      learnMore: '了解更多',
      features: '主要特性',
      products: [
        {
          title: '吸水膨胀袋',
          description: '采用瞬间吸水膨胀技术的快速部署防洪保护',
          category: '应急保护',
          href: '/products/water-absorbing-bags',
          icon: Droplets,
          color: 'blue',
          features: [
            '3-5分钟快速激活',
            '膨胀至原体积20倍',
            '轻便易携带',
            '环保材料',
            '5年保质期',
          ],
          popular: true,
        },
        {
          title: 'ABS组合式防洪板',
          description: '专业模块化防洪保护系统，具有卓越的耐用性',
          category: '永久保护',
          href: '/products/abs-flood-barriers',
          icon: Shield,
          color: 'slate',
          features: [
            '模块化联锁设计',
            '高强度ABS结构',
            '抗紫外线耐候',
            '免工具组装',
            '20年以上寿命',
          ],
          popular: false,
        },
        {
          title: '铝合金定制挡水板',
          description: '高端定制工程铝合金防洪板，适用于关键应用场景',
          category: '高端解决方案',
          href: '/products/aluminum-water-barriers',
          icon: Award,
          color: 'amber',
          features: [
            '船用级铝合金',
            '定制工程设计',
            '耐腐蚀处理',
            '美观集成',
            '25年保修',
          ],
          popular: false,
        },
      ],
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-6">
            {currentContent.title}
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {currentContent.products.map((product, index) => {
            const IconComponent = product.icon;
            const colorClasses: Record<string, string> = {
              blue: 'bg-secondary',
              slate: 'bg-primary',
              amber: 'bg-accent',
            };

            return (
              <Card
                key={index}
                className={`relative hover:shadow-lg transition-all duration-300 hover:scale-105 ${
                  product.popular ? 'ring-2 ring-primary ring-offset-2' : ''
                }`}
              >
                {product.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                      <Star className="h-3 w-3 fill-current" />
                      {locale === 'en' ? 'Popular' : '热门'}
                    </div>
                  </div>
                )}

                <CardHeader>
                  <div className="flex items-center gap-3 mb-2">
                    <div
                      className={`p-3 ${colorClasses[product.color]} rounded-lg`}
                    >
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-xl">{product.title}</CardTitle>
                  <CardDescription className="text-base">
                    {product.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-secondary" />
                        {currentContent.features}
                      </h4>
                      <ul className="space-y-2">
                        {product.features.map((feature, featureIndex) => (
                          <li
                            key={featureIndex}
                            className="flex items-center gap-2 text-sm"
                          >
                            <CheckCircle className="h-3 w-3 text-secondary flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="flex gap-2 pt-4">
                      <LocalizedNavigationButton
                        href={product.href}
                        className="flex-1"
                        size="sm"
                        showIcon={false}
                      >
                        {currentContent.viewDetails}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </LocalizedNavigationButton>
                      <ContactButton
                        email="<EMAIL>"
                        subject={`${locale === 'en' ? 'Inquiry about' : '咨询'} ${product.title}`}
                        variant="outline"
                        size="sm"
                        showIcon={false}
                      >
                        {currentContent.learnMore}
                      </ContactButton>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
}
