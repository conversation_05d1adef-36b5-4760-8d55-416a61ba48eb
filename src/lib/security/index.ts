/**
 * 安全模块入口文件
 * 
 * 统一导出所有安全相关的功能
 */

// 配置相关
export {
  CSP_CONFIG,
  SECURITY_HEADERS,
  HSTS_CONFIG,
  CORS_CONFIG,
  generateCSP,
  getSecurityHeaders,
  isOriginAllowed,
  validateSecurityConfig,
} from './config';

// 中间件相关
export {
  handleCORS,
  addSecurityHeaders,
  securityMiddleware,
  wrapWithSecurity,
  isTrustedRequest,
  createSecureResponse,
  secureRedirect,
  logSecurityEvent,
} from './middleware';

// 工具函数
export {
  generateSecureToken,
  generateNonce,
  sanitizeInput,
  isSecureUrl,
  getClientIP,
  isBotRequest,
  getCSPNonce,
  validateCSRFToken,
  getSecureCookieOptions,
  detectSuspiciousHeaders,
  safeJSONParse,
  limitStringLength,
  isSecureFileType,
  generateSecurityReport,
} from './utils';

// 类型定义
export interface SecurityConfig {
  csp: Record<string, string[]>;
  headers: Record<string, string>;
  cors: {
    allowedOrigins: string[];
    allowedMethods: string[];
    allowedHeaders: string[];
    credentials: boolean;
    maxAge: number;
  };
}

export interface SecurityReport {
  timestamp: string;
  clientIP: string | null;
  isBot: boolean;
  suspiciousHeaders: string[];
  userAgent: string | null;
  origin: string | null;
  referer: string | null;
}

export interface SecurityValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 常量
export const SECURITY_CONSTANTS = {
  // Token 长度
  DEFAULT_TOKEN_LENGTH: 32,
  NONCE_LENGTH: 32,
  
  // 超时时间
  CSRF_TOKEN_EXPIRY: 60 * 60 * 1000, // 1 小时
  SESSION_TIMEOUT: 60 * 60 * 24 * 7 * 1000, // 7 天
  
  // 限制
  MAX_INPUT_LENGTH: 1000,
  MAX_HEADER_LENGTH: 8192,
  
  // 文件类型
  ALLOWED_FILE_EXTENSIONS: [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    '.pdf', '.txt', '.md', '.json',
    '.mp4', '.webm', '.mp3', '.wav',
  ],
} as const;
