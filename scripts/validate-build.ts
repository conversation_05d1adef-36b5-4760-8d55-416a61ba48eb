#!/usr/bin/env tsx

/**
 * 构建验证脚本
 *
 * 这个脚本验证：
 * 1. TypeScript 编译无错误
 * 2. ESLint 检查通过
 * 3. 组件边界测试通过
 * 4. 静态生成验证通过
 * 5. Next.js 构建成功
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface ValidationResult {
  step: string;
  success: boolean;
  error?: string;
  duration: number;
}

class BuildValidator {
  private results: ValidationResult[] = [];

  /**
   * 执行命令并记录结果
   */
  private async runStep(step: string, command: string): Promise<boolean> {
    const startTime = Date.now();

    try {
      console.log(`🔍 ${step}...`);
      execSync(command, { stdio: 'inherit', cwd: process.cwd() });

      const duration = Date.now() - startTime;
      this.results.push({ step, success: true, duration });
      console.log(`✅ ${step} completed in ${duration}ms\n`);
      return true;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.results.push({
        step,
        success: false,
        error: errorMessage,
        duration,
      });
      console.error(`❌ ${step} failed in ${duration}ms`);
      console.error(`Error: ${errorMessage}\n`);
      return false;
    }
  }

  /**
   * 验证项目结构
   */
  private validateProjectStructure(): boolean {
    console.log('🔍 Validating project structure...');

    const requiredFiles = [
      'package.json',
      'tsconfig.json',
      'next.config.ts',
      'vitest.config.ts',
      'src/app/layout.tsx',
      'src/components/interactive/index.ts',
    ];

    const requiredDirs = [
      'src/app',
      'src/components',
      'src/components/interactive',
      '__tests__',
      '__tests__/components/interactive',
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        console.error(`❌ Required file missing: ${file}`);
        return false;
      }
    }

    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        console.error(`❌ Required directory missing: ${dir}`);
        return false;
      }
    }

    console.log('✅ Project structure validation passed\n');
    return true;
  }

  /**
   * 检查是否有 force-dynamic 配置
   */
  private checkForceDynamic(): boolean {
    console.log('🔍 Checking for force-dynamic configurations...');

    const srcDir = path.join(process.cwd(), 'src');
    const files = this.getAllFiles(srcDir, ['.ts', '.tsx']);

    for (const file of files) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        if (content.includes("export const dynamic = 'force-dynamic'")) {
          console.error(`❌ Found force-dynamic in: ${file}`);
          return false;
        }
      } catch {
        // 忽略读取错误
      }
    }

    console.log('✅ No force-dynamic configurations found\n');
    return true;
  }

  /**
   * 递归获取所有文件
   */
  private getAllFiles(dir: string, extensions: string[]): string[] {
    const files: string[] = [];

    if (!fs.existsSync(dir)) {
      return files;
    }

    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (
        stat.isDirectory() &&
        !item.startsWith('.') &&
        item !== 'node_modules'
      ) {
        files.push(...this.getAllFiles(fullPath, extensions));
      } else if (extensions.some((ext) => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }

    return files;
  }

  /**
   * 运行完整的验证流程
   */
  async validate(): Promise<boolean> {
    console.log('🚀 Starting build validation...\n');

    // 1. 验证项目结构
    if (!this.validateProjectStructure()) {
      return false;
    }

    // 2. 检查 force-dynamic 配置
    if (!this.checkForceDynamic()) {
      return false;
    }

    // 3. TypeScript 类型检查
    if (
      !(await this.runStep('TypeScript type checking', 'npm run typecheck'))
    ) {
      return false;
    }

    // 4. ESLint 检查
    if (!(await this.runStep('ESLint checking', 'npm run lint'))) {
      return false;
    }

    // 5. 组件边界测试
    if (
      !(await this.runStep(
        'Component boundary tests',
        'npm run test:boundaries'
      ))
    ) {
      return false;
    }

    // 6. 集成测试
    if (
      !(await this.runStep('Integration tests', 'npm run test:integration'))
    ) {
      return false;
    }

    // 7. Next.js 构建
    if (!(await this.runStep('Next.js build', 'npm run build'))) {
      return false;
    }

    return true;
  }

  /**
   * 打印验证结果摘要
   */
  printSummary(): void {
    console.log('\n📊 Validation Summary:');
    console.log('='.repeat(50));

    let totalDuration = 0;
    let passedSteps = 0;

    for (const result of this.results) {
      const status = result.success ? '✅' : '❌';
      const duration = `${result.duration}ms`;
      console.log(`${status} ${result.step.padEnd(30)} ${duration}`);

      totalDuration += result.duration;
      if (result.success) passedSteps++;
    }

    console.log('='.repeat(50));
    console.log(`Total steps: ${this.results.length}`);
    console.log(`Passed: ${passedSteps}`);
    console.log(`Failed: ${this.results.length - passedSteps}`);
    console.log(`Total duration: ${totalDuration}ms`);

    if (passedSteps === this.results.length) {
      console.log(
        '\n🎉 All validations passed! Build is ready for deployment.'
      );
    } else {
      console.log(
        '\n💥 Some validations failed. Please fix the issues before deployment.'
      );
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const validator = new BuildValidator();

  try {
    const success = await validator.validate();
    validator.printSummary();

    process.exit(success ? 0 : 1);
  } catch (error) {
    console.error('💥 Validation failed with unexpected error:');
    console.error(error);
    process.exit(1);
  }
}

// 运行验证
if (require.main === module) {
  main();
}

export { BuildValidator };
