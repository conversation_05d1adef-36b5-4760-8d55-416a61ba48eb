#!/usr/bin/env tsx

/**
 * 安全配置验证脚本
 * 
 * 验证项目的安全配置是否正确
 */

import { validateSecurityConfig, generateCSP } from '../src/lib/security';
import { env } from '../src/env';

async function main() {
  console.log('🔒 验证安全配置...\n');

  // 1. 验证基础安全配置
  const validation = validateSecurityConfig();
  
  if (!validation.isValid) {
    console.error('❌ 安全配置验证失败:');
    validation.errors.forEach(error => {
      console.error(`  - ${error}`);
    });
    process.exit(1);
  }

  if (validation.warnings.length > 0) {
    console.warn('⚠️  安全配置警告:');
    validation.warnings.forEach(warning => {
      console.warn(`  - ${warning}`);
    });
    console.log();
  }

  // 2. 验证环境变量
  console.log('📋 环境变量检查:');
  
  const requiredVars = ['NEXT_PUBLIC_APP_URL'];
  const optionalVars = [
    'ARCJET_KEY',
    'SECURITY_SECRET',
    'CSRF_SECRET',
    'RESEND_TOKEN',
    'UPSTASH_REDIS_REST_URL',
  ];

  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: 已设置`);
    } else {
      console.error(`  ❌ ${varName}: 未设置 (必需)`);
    }
  });

  optionalVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`  ✅ ${varName}: 已设置`);
    } else {
      console.log(`  ⚪ ${varName}: 未设置 (可选)`);
    }
  });

  console.log();

  // 3. 验证 CSP 配置
  console.log('🛡️  CSP 配置检查:');
  try {
    const csp = generateCSP();
    console.log(`  ✅ CSP 生成成功`);
    console.log(`  📝 CSP 策略长度: ${csp.length} 字符`);
    
    // 检查关键指令
    const requiredDirectives = [
      'default-src',
      'script-src',
      'style-src',
      'img-src',
      'font-src',
      'connect-src',
    ];
    
    requiredDirectives.forEach(directive => {
      if (csp.includes(directive)) {
        console.log(`  ✅ ${directive}: 已配置`);
      } else {
        console.warn(`  ⚠️  ${directive}: 未找到`);
      }
    });
  } catch (error) {
    console.error(`  ❌ CSP 生成失败: ${error}`);
  }

  console.log();

  // 4. 验证 HTTPS 配置
  console.log('🔐 HTTPS 配置检查:');
  try {
    const appUrl = new URL(env.NEXT_PUBLIC_APP_URL);
    if (appUrl.protocol === 'https:') {
      console.log('  ✅ 使用 HTTPS 协议');
    } else if (process.env.NODE_ENV === 'production') {
      console.error('  ❌ 生产环境应使用 HTTPS');
    } else {
      console.log('  ⚪ 开发环境使用 HTTP (正常)');
    }
  } catch (error) {
    console.error(`  ❌ URL 解析失败: ${error}`);
  }

  console.log();

  // 5. 安全建议
  console.log('💡 安全建议:');
  
  const recommendations = [];
  
  if (!env.ARCJET_KEY) {
    recommendations.push('配置 ARCJET_KEY 以启用高级安全防护');
  }
  
  if (!env.SECURITY_SECRET) {
    recommendations.push('配置 SECURITY_SECRET 以增强安全性');
  }
  
  if (!env.CSRF_SECRET) {
    recommendations.push('配置 CSRF_SECRET 以防止 CSRF 攻击');
  }
  
  if (process.env.NODE_ENV === 'production' && !env.NEXT_PUBLIC_SENTRY_DSN) {
    recommendations.push('配置 Sentry 以监控生产环境错误');
  }

  if (recommendations.length === 0) {
    console.log('  ✅ 所有安全配置都已优化');
  } else {
    recommendations.forEach(rec => {
      console.log(`  💡 ${rec}`);
    });
  }

  console.log();

  // 6. 生成安全报告
  console.log('📊 安全配置摘要:');
  console.log(`  - 环境: ${process.env.NODE_ENV || 'development'}`);
  console.log(`  - 应用 URL: ${env.NEXT_PUBLIC_APP_URL}`);
  console.log(`  - CSP 启用: ✅`);
  console.log(`  - 安全头部: ✅`);
  console.log(`  - CORS 配置: ✅`);
  console.log(`  - 高级防护: ${env.ARCJET_KEY ? '✅' : '⚪'}`);

  console.log('\n🎉 安全配置验证完成!');
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的错误:', error);
  process.exit(1);
});

// 运行验证
main().catch((error) => {
  console.error('❌ 验证失败:', error);
  process.exit(1);
});
