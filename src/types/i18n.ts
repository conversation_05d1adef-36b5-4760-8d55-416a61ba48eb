import { type Locale } from '@/lib/i18n/config';

export interface PageParams {
  locale: Locale;
}

export interface NavigationItem {
  href: string;
  label: Record<Locale, string>;
  description?: Record<Locale, string>;
  children?: NavigationItem[];
}

export interface PageMetadata {
  title: Record<Locale, string>;
  description: Record<Locale, string>;
  keywords?: Record<Locale, string[]>;
}

export interface ContentMeta {
  title: string;
  description: string;
  date?: string;
  author?: string;
  tags?: string[];
  featured?: boolean;
}

export interface BlogPost extends ContentMeta {
  slug: string;
  content: string;
  locale: Locale;
}

export interface Product extends ContentMeta {
  slug: string;
  content: string;
  locale: Locale;
  price?: string;
  features?: string[];
  category?: string;
}
