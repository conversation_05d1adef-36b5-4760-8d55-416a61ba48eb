import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderWithProviders, screen, userEvent } from '@/lib/testing/utils';
import { LanguageSwitcher } from '@/components/navigation/language-switcher';

// Mock next/navigation
const mockPush = vi.fn();
vi.mock('next/navigation', () => ({
  usePathname: () => '/en',
  useRouter: () => ({
    push: mockPush,
  }),
}));

describe('LanguageSwitcher Component', () => {
  beforeEach(() => {
    mockPush.mockClear();
  });

  it('renders with current locale', () => {
    renderWithProviders(<LanguageSwitcher currentLocale="en" />);

    expect(screen.getByRole('button', { name: /EN/i })).toBeInTheDocument();
  });

  it('shows language options when clicked', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LanguageSwitcher currentLocale="en" />);

    const trigger = screen.getByRole('button', { name: /EN/i });
    await user.click(trigger);

    expect(screen.getByText('English')).toBeInTheDocument();
    expect(screen.getAllByText('中文')).toHaveLength(2); // Label and name both show 中文
  });

  it('switches language when option is selected', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LanguageSwitcher currentLocale="en" />);

    const trigger = screen.getByRole('button', { name: /EN/i });
    await user.click(trigger);

    const chineseOptions = screen.getAllByText('中文');
    await user.click(chineseOptions[0]); // Click the first one (font-medium)

    expect(mockPush).toHaveBeenCalledWith('/zh/');
  });

  it('highlights current locale', async () => {
    const user = userEvent.setup();
    renderWithProviders(<LanguageSwitcher currentLocale="zh" />);

    const trigger = screen.getByRole('button');
    await user.click(trigger);

    // Find the menu item with bg-accent class (current locale)
    const menuItems = screen.getAllByRole('menuitem');
    const currentLocaleItem = menuItems.find((item) =>
      item.classList.contains('bg-accent')
    );
    expect(currentLocaleItem).toBeInTheDocument();
  });
});
