name: Lingo.dev i18n

on:
  push:
    branches:
      - main

permissions:
  contents: write
  pull-requests: write

jobs:
  i18n:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      - uses: lingodotdev/lingo.dev@main
        env:
          GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
          LINGODOTDEV_API_KEY: ${{ secrets.LINGODOTDEV_API_KEY }}
          GH_TOKEN: ${{ github.token }}
        with:
          api-key: ${{ secrets.LINGODOTDEV_API_KEY }}