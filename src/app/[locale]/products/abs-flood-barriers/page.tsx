import { type Locale } from '@/lib/i18n/config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PhoneButton, EmailButton } from '@/components/interactive';
import {
  Shield,
  Layers,
  Wrench,
  CheckCircle,
  ArrowLeft,
  MapPin,
  Star,
  Package,
} from 'lucide-react';
import Link from 'next/link';

interface ProductPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function ABSFloodBarriersPage({
  params,
}: ProductPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'ABS Modular Flood Barriers',
      subtitle:
        'Professional modular flood protection system with superior durability',
      backToProducts: 'Back to Products',
      contactUs: 'Contact Us',
      getQuote: 'Get Quote',
      overview: 'Product Overview',
      features: 'Key Features',
      specifications: 'Technical Specifications',
      applications: 'Applications',
      advantages: 'Product Advantages',
      description:
        'Our ABS modular flood barriers provide robust, long-term flood protection through innovative interlocking design. Made from high-grade ABS plastic, these barriers offer superior strength, weather resistance, and easy assembly for both temporary and permanent installations.',
      featuresList: [
        'Modular interlocking design',
        'High-strength ABS construction',
        'UV and weather resistant',
        'Tool-free assembly system',
        'Stackable for various heights',
        'Reusable and maintenance-free',
      ],
      specs: {
        material: 'Material',
        materialValue: 'High-grade ABS plastic',
        dimensions: 'Standard Module Size',
        dimensionsValue: '100cm × 50cm × 30cm',
        weight: 'Weight per Module',
        weightValue: '8.5kg',
        height: 'Maximum Height',
        heightValue: 'Up to 1.5m (stackable)',
        temperature: 'Operating Temperature',
        temperatureValue: '-40°C to +80°C',
        lifespan: 'Service Life',
        lifespanValue: '20+ years',
      },
      applicationsList: [
        'Permanent flood protection',
        'Industrial facility protection',
        'Urban flood management',
        'Critical infrastructure safeguarding',
        'Coastal protection systems',
        'Emergency response deployment',
      ],
      advantagesList: [
        {
          title: 'Modular Flexibility',
          description:
            'Customizable configurations to fit any site requirement and terrain.',
        },
        {
          title: 'Superior Durability',
          description:
            'ABS construction ensures 20+ years of reliable service in harsh conditions.',
        },
        {
          title: 'Easy Installation',
          description:
            'Tool-free interlocking system allows rapid deployment by minimal crew.',
        },
        {
          title: 'Cost Effective',
          description:
            'Long-term solution with minimal maintenance and operational costs.',
        },
      ],
    },
    zh: {
      title: 'ABS组合式防洪板',
      subtitle: '专业模块化防洪保护系统，具有卓越的耐用性',
      backToProducts: '返回产品',
      contactUs: '联系我们',
      getQuote: '获取报价',
      overview: '产品概述',
      features: '主要特性',
      specifications: '技术规格',
      applications: '应用场景',
      advantages: '产品优势',
      description:
        '我们的ABS组合式防洪板通过创新的联锁设计提供坚固的长期防洪保护。采用高级ABS塑料制造，这些防洪板具有卓越的强度、耐候性，以及便于临时和永久安装的简易组装特性。',
      featuresList: [
        '模块化联锁设计',
        '高强度ABS结构',
        '抗紫外线和耐候性',
        '免工具组装系统',
        '可堆叠不同高度',
        '可重复使用且免维护',
      ],
      specs: {
        material: '材料',
        materialValue: '高级ABS塑料',
        dimensions: '标准模块尺寸',
        dimensionsValue: '100厘米 × 50厘米 × 30厘米',
        weight: '每模块重量',
        weightValue: '8.5公斤',
        height: '最大高度',
        heightValue: '最高1.5米（可堆叠）',
        temperature: '工作温度',
        temperatureValue: '-40°C 至 +80°C',
        lifespan: '使用寿命',
        lifespanValue: '20年以上',
      },
      applicationsList: [
        '永久性防洪保护',
        '工业设施保护',
        '城市防洪管理',
        '关键基础设施防护',
        '海岸保护系统',
        '应急响应部署',
      ],
      advantagesList: [
        {
          title: '模块化灵活性',
          description: '可定制配置以适应任何场地要求和地形。',
        },
        {
          title: '卓越耐用性',
          description: 'ABS结构确保在恶劣条件下20年以上的可靠服务。',
        },
        {
          title: '易于安装',
          description: '免工具联锁系统允许最少人员快速部署。',
        },
        {
          title: '成本效益',
          description: '长期解决方案，维护和运营成本最低。',
        },
      ],
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <Link
            href={`/${locale === 'en' ? '' : locale + '/'}products`}
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentContent.backToProducts}
          </Link>
        </div>

        {/* Hero Section */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="p-3 bg-slate-100 dark:bg-slate-800 rounded-lg">
                <Shield className="h-8 w-8 text-slate-600 dark:text-slate-400" />
              </div>
              <Badge variant="secondary" className="text-sm">
                {currentContent.features}
              </Badge>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-slate-600 to-blue-600 bg-clip-text text-transparent mb-6">
              {currentContent.title}
            </h1>

            <p className="text-xl text-slate-600 dark:text-slate-300 mb-8">
              {currentContent.subtitle}
            </p>

            <div className="flex gap-4">
              <PhoneButton
                phone="+****************"
                size="lg"
                className="bg-slate-600 hover:bg-slate-700"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-slate-100 to-blue-100 dark:from-slate-800 dark:to-blue-800 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <Layers className="h-32 w-32 text-slate-500 mx-auto mb-4" />
                <p className="text-slate-600 dark:text-slate-300">
                  {currentContent.title}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Overview */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {currentContent.overview}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
              {currentContent.description}
            </p>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layers className="h-5 w-5 text-blue-500" />
                {currentContent.features}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.featuresList.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Wrench className="h-5 w-5 text-orange-500" />
                {currentContent.specifications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.material}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.materialValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.dimensions}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.dimensionsValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.weight}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.weightValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.height}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.heightValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.temperature}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.temperatureValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.lifespan}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.lifespanValue}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                {currentContent.applications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.applicationsList.map((application, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Star className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{application}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Advantages */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              {currentContent.advantages}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {currentContent.advantagesList.map((advantage, index) => (
                <div
                  key={index}
                  className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
                >
                  <h4 className="font-semibold mb-2">{advantage.title}</h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    {advantage.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-slate-600 to-blue-600 text-white">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'en'
                ? 'Need Professional Flood Protection?'
                : '需要专业防洪保护吗？'}
            </h3>
            <p className="text-slate-100 mb-6">
              {locale === 'en'
                ? 'Contact our engineers for customized modular barrier solutions.'
                : '联系我们的工程师获取定制化模块防洪解决方案。'}
            </p>
            <div className="flex gap-4 justify-center">
              <PhoneButton
                phone="+****************"
                size="lg"
                variant="secondary"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
                className="text-white border-white hover:bg-white hover:text-slate-600"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
