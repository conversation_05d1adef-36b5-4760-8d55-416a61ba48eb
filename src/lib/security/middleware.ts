/**
 * 安全中间件工具
 * 
 * 提供中间件中使用的安全功能，包括 CORS 处理、安全头部设置等
 */

import { NextRequest, NextResponse } from 'next/server';
import { CORS_CONFIG, getSecurityHeaders, isOriginAllowed } from './config';

/**
 * 处理 CORS 预检请求
 */
export function handleCORS(request: NextRequest): NextResponse | null {
  const origin = request.headers.get('origin');
  const method = request.method;

  // 处理预检请求
  if (method === 'OPTIONS') {
    // 检查源是否被允许
    if (origin && !isOriginAllowed(origin)) {
      return new NextResponse(null, { status: 403 });
    }

    const response = new NextResponse(null, { status: 200 });

    // 设置 CORS 头部
    if (origin) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }

    response.headers.set(
      'Access-Control-Allow-Methods',
      CORS_CONFIG.allowedMethods.join(', ')
    );

    response.headers.set(
      'Access-Control-Allow-Headers',
      CORS_CONFIG.allowedHeaders.join(', ')
    );

    if (CORS_CONFIG.credentials) {
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }

    response.headers.set(
      'Access-Control-Max-Age',
      CORS_CONFIG.maxAge.toString()
    );

    return response;
  }

  return null;
}

/**
 * 为响应添加安全头部
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  const securityHeaders = getSecurityHeaders();

  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  // 添加 CORS 头部（如果需要）
  const origin = response.headers.get('origin');
  if (origin && isOriginAllowed(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);

    if (CORS_CONFIG.credentials) {
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
  }

  return response;
}

/**
 * 安全中间件主函数
 * 
 * 在现有的国际化中间件基础上添加安全功能
 */
export function securityMiddleware(request: NextRequest): NextResponse | null {
  // 1. 处理 CORS 预检请求
  const corsResponse = handleCORS(request);
  if (corsResponse) {
    return corsResponse;
  }

  // 2. 检查请求频率限制（如果配置了 Arcjet）
  // 这里可以添加更多的安全检查逻辑

  return null;
}

/**
 * 包装响应以添加安全头部
 */
export function wrapWithSecurity(response: NextResponse): NextResponse {
  return addSecurityHeaders(response);
}

/**
 * 检查请求是否来自可信源
 */
export function isTrustedRequest(request: NextRequest): boolean {
  const origin = request.headers.get('origin');
  const referer = request.headers.get('referer');

  // 同源请求总是可信的
  if (!origin && !referer) {
    return true;
  }

  // 检查 origin
  if (origin && !isOriginAllowed(origin)) {
    return false;
  }

  // 检查 referer
  if (referer) {
    try {
      const refererUrl = new URL(referer);
      if (!isOriginAllowed(refererUrl.origin)) {
        return false;
      }
    } catch {
      return false;
    }
  }

  return true;
}

/**
 * 生成安全的响应
 */
export function createSecureResponse(
  body?: BodyInit | null,
  init?: ResponseInit
): NextResponse {
  const response = new NextResponse(body, init);
  return addSecurityHeaders(response);
}

/**
 * 安全重定向
 * 
 * 确保重定向目标是安全的
 */
export function secureRedirect(
  url: string,
  request: NextRequest,
  init?: ResponseInit
): NextResponse {
  try {
    const redirectUrl = new URL(url, request.url);

    // 只允许重定向到同域或配置的可信域
    if (redirectUrl.origin !== new URL(request.url).origin) {
      if (!isOriginAllowed(redirectUrl.origin)) {
        // 如果目标不可信，重定向到首页
        return NextResponse.redirect(new URL('/', request.url));
      }
    }

    const response = NextResponse.redirect(redirectUrl, init);
    return addSecurityHeaders(response);
  } catch {
    // URL 解析失败，重定向到首页
    const response = NextResponse.redirect(new URL('/', request.url));
    return addSecurityHeaders(response);
  }
}

/**
 * 日志记录安全事件
 */
export function logSecurityEvent(
  event: string,
  request: NextRequest,
  details?: Record<string, any>
): void {
  if (process.env.NODE_ENV === 'development') {
    console.warn(`[Security] ${event}`, {
      url: request.url,
      method: request.method,
      origin: request.headers.get('origin'),
      userAgent: request.headers.get('user-agent'),
      ...details,
    });
  }

  // 在生产环境中，这里可以发送到日志服务
  // 例如：Sentry、LogRocket 等
}
