# Test Translation File

This is a test file to verify Lingo.dev translation functionality.

## Features to Test

### Basic Translation

- Simple text translation
- Markdown formatting preservation
- Code block handling

### Advanced Features

- **Bold text** translation
- _Italic text_ translation
- [Link text](https://example.com) translation

### Code Examples

```javascript
// This code should not be translated
function hello() {
  console.log('Hello, World!');
}
```

### Lists

1. First item to translate
2. Second item to translate
3. Third item to translate

- Bullet point one
- Bullet point two
- Bullet point three

### Technical Terms

This file contains technical terms like:

- React components
- Next.js routing
- TypeScript interfaces
- API endpoints

### Special Characters

Testing special characters: @#$%^&\*()\_+-={}[]|;':",./<>?

### Emoji Support

Testing emoji support: 🚀 🎉 ✅ ❌ 🔧 📊

## Conclusion

This test file will help verify that Lingo.dev can properly translate content while preserving formatting and handling edge cases.
