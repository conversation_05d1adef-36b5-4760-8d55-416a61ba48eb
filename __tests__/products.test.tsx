import { describe, it, expect } from 'vitest';
import {
  renderAsyncComponent,
  screen,
  waitForAsyncComponent,
} from '@/lib/testing/utils';
import { createProductsTestWrapper } from '@/lib/testing/async-component-wrapper';

describe('Products Page', () => {
  const ProductsTestWrapper = createProductsTestWrapper();

  it('renders products page in English', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getByText('Flood Protection Solutions')).toBeInTheDocument();
    expect(screen.getByText('Emergency Protection')).toBeInTheDocument();
    expect(screen.getByText('Water Absorbing Bags')).toBeInTheDocument();
  });

  it('renders products page in Chinese', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'zh' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getByText('Flood Protection Solutions')).toBeInTheDocument();
    expect(screen.getByText('Emergency Protection')).toBeInTheDocument();
    expect(screen.getByText('Water Absorbing Bags')).toBeInTheDocument();
  });

  it('displays product features', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getByText('Rapid 3-5 minute activation')).toBeInTheDocument();
    expect(screen.getByText('Expands 20x original size')).toBeInTheDocument();
    expect(screen.getByText('Lightweight and portable')).toBeInTheDocument();
  });

  it('shows popular product badge', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getByText('Popular')).toBeInTheDocument();
  });

  it('displays action buttons', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    const viewDetailsButtons = screen.getAllByText('View Details');
    const learnMoreButtons = screen.getAllByText('Learn More');

    expect(viewDetailsButtons.length).toBeGreaterThan(0);
    expect(learnMoreButtons.length).toBeGreaterThan(0);
  });

  it('shows contact information', async () => {
    await renderAsyncComponent(
      <ProductsTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    // 检查是否有联系相关的内容
    expect(document.body).toBeInTheDocument();
  });
});
