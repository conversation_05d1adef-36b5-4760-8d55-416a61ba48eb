import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { EmailButton, PhoneButton, ContactButton } from '../contact-button';

// Mock window.location
const mockLocation = {
  href: '',
};

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

// Mock window.open
const mockOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockOpen,
  writable: true,
});

describe('Contact Button Components', () => {
  beforeEach(() => {
    mockLocation.href = '';
    mockOpen.mockClear();
  });

  describe('EmailButton', () => {
    it('renders with email icon and text', () => {
      render(<EmailButton email="<EMAIL>">Send Email</EmailButton>);

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByText('Send Email')).toBeInTheDocument();
      expect(
        screen.getByLabelText('Send <NAME_EMAIL>')
      ).toBeInTheDocument();
    });

    it('opens mailto link when clicked', () => {
      render(<EmailButton email="<EMAIL>">Send Email</EmailButton>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('mailto:<EMAIL>');
    });

    it('includes subject and body in mailto link', () => {
      render(
        <EmailButton
          email="<EMAIL>"
          subject="Test Subject"
          body="Test Body"
        >
          Send Email
        </EmailButton>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe(
        'mailto:<EMAIL>?subject=Test+Subject&body=Test+Body'
      );
    });

    it('renders without icon when showIcon is false', () => {
      render(
        <EmailButton email="<EMAIL>" showIcon={false}>
          Send Email
        </EmailButton>
      );

      const button = screen.getByRole('button');
      expect(button.querySelector('svg')).toBeNull();
    });
  });

  describe('PhoneButton', () => {
    it('renders with phone icon and text', () => {
      render(<PhoneButton phone="******-123-4567">Call Now</PhoneButton>);

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByText('Call Now')).toBeInTheDocument();
      expect(screen.getByLabelText('Call ******-123-4567')).toBeInTheDocument();
    });

    it('opens tel link when clicked', () => {
      render(<PhoneButton phone="******-123-4567">Call Now</PhoneButton>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('tel:******-123-4567');
    });

    it('cleans phone number format', () => {
      render(<PhoneButton phone="+****************">Call Now</PhoneButton>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('tel:+****************');
    });
  });

  describe('ContactButton', () => {
    it('uses email as preferred method by default', () => {
      render(
        <ContactButton email="<EMAIL>" phone="******-123-4567">
          Contact Us
        </ContactButton>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('mailto:<EMAIL>');
    });

    it('uses phone when specified as preferred method', () => {
      render(
        <ContactButton
          email="<EMAIL>"
          phone="******-123-4567"
          preferredMethod="phone"
        >
          Contact Us
        </ContactButton>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('tel:******-123-4567');
    });

    it('falls back to available method when preferred is not available', () => {
      render(
        <ContactButton phone="******-123-4567" preferredMethod="email">
          Contact Us
        </ContactButton>
      );

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockLocation.href).toBe('tel:******-123-4567');
    });

    it('shows appropriate icon based on preferred method', () => {
      const { rerender } = render(
        <ContactButton
          email="<EMAIL>"
          phone="******-123-4567"
          preferredMethod="email"
        >
          Contact Us
        </ContactButton>
      );

      expect(
        screen.getByLabelText('Send <NAME_EMAIL>')
      ).toBeInTheDocument();

      rerender(
        <ContactButton
          email="<EMAIL>"
          phone="******-123-4567"
          preferredMethod="phone"
        >
          Contact Us
        </ContactButton>
      );

      expect(screen.getByLabelText('Call ******-123-4567')).toBeInTheDocument();
    });

    it('warns when no contact method is provided', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      render(<ContactButton>Contact Us</ContactButton>);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(consoleSpy).toHaveBeenCalledWith(
        'ContactButton: No email or phone provided'
      );

      consoleSpy.mockRestore();
    });
  });
});
