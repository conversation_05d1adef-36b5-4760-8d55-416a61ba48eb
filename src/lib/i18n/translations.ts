import { type Locale } from './config';

// 翻译键的类型定义
export interface Translations {
  // 通用UI文案
  common: {
    loading: string;
    error: string;
    success: string;
    cancel: string;
    confirm: string;
    save: string;
    edit: string;
    delete: string;
    back: string;
    next: string;
    previous: string;
    close: string;
    search: string;
    filter: string;
    sort: string;
    more: string;
    less: string;
  };

  // 导航相关
  navigation: {
    home: string;
    products: string;
    blog: string;
    about: string;
    contact: string;
    login: string;
    logout: string;
    profile: string;
    settings: string;
  };

  // 首页内容
  home: {
    title: string;
    subtitle: string;
    getStarted: string;
    learnMore: string;
    toggleTheme: string;
    configComplete: string;
    configDescription: string;
    features: {
      nextjs: {
        title: string;
        description: string;
      };
      ui: {
        title: string;
        description: string;
      };
      state: {
        title: string;
        description: string;
      };
      typescript: {
        title: string;
        description: string;
      };
      testing: {
        title: string;
        description: string;
      };
      ai: {
        title: string;
        description: string;
      };
    };
    checklist: string[];
  };

  // 产品页面
  products: {
    title: string;
    subtitle: string;
    framework: {
      title: string;
      description: string;
      features: string[];
    };
    components: {
      title: string;
      description: string;
      features: string[];
    };
    tools: {
      title: string;
      description: string;
      features: string[];
    };
    pricing: {
      free: string;
      getStarted: string;
      learnMore: string;
    };
    badges: {
      popular: string;
      recommended: string;
      new: string;
    };
  };

  // 博客页面
  blog: {
    title: string;
    subtitle: string;
    readMore: string;
    publishedOn: string;
    author: string;
    tags: string;
    categories: string;
    relatedPosts: string;
    noPostsFound: string;
  };

  // 关于页面
  about: {
    title: string;
    subtitle: string;
    mission: string;
    vision: string;
    values: string;
    team: string;
    history: string;
    contact: string;
  };

  // 表单相关
  forms: {
    validation: {
      required: string;
      email: string;
      minLength: string;
      maxLength: string;
      pattern: string;
    };
    contact: {
      name: string;
      email: string;
      subject: string;
      message: string;
      submit: string;
      success: string;
      error: string;
    };
  };

  // SEO 元数据
  meta: {
    defaultTitle: string;
    defaultDescription: string;
    keywords: string[];
  };
}

// 英文翻译（默认语言）
export const enTranslations: Translations = {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    more: 'More',
    less: 'Less',
  },

  navigation: {
    home: 'Home',
    products: 'Products',
    blog: 'Blog',
    about: 'About',
    contact: 'Contact',
    login: 'Login',
    logout: 'Logout',
    profile: 'Profile',
    settings: 'Settings',
  },

  home: {
    title: 'Tucsenberg Web 1.0',
    subtitle:
      'Modern frontend technology solution - Based on Next.js 15 and React 19 with AI-powered translation',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    toggleTheme: 'Toggle Theme',
    configComplete: 'Tech Stack Configuration Complete ✅',
    configDescription:
      'Project has successfully configured the following core features including Lingo.dev AI translation system with BYOK Google Gemini integration - now fully operational',
    features: {
      nextjs: {
        title: 'Next.js 15 + React 19',
        description: 'Latest React and Next.js technology stack',
      },
      ui: {
        title: 'shadcn/ui + Tailwind CSS',
        description: 'Modern design system and styling framework',
      },
      state: {
        title: 'Zustand + React Query',
        description: 'Efficient state management and data fetching',
      },
      typescript: {
        title: 'TypeScript + ESLint',
        description: 'Type safety and code quality assurance',
      },
      testing: {
        title: 'Vitest + Testing Library',
        description: 'Complete testing solution',
      },
      ai: {
        title: 'AI Anti-pattern Protection',
        description: 'Avoid common AI-generated errors',
      },
    },
    checklist: [
      'Next.js 15 + React 19',
      'TypeScript Configuration',
      'Tailwind CSS + shadcn/ui',
      'Zustand State Management',
      'React Query Data Fetching',
      'Vitest Testing Framework',
      'ESLint + Prettier',
      'Husky Git Hooks',
    ],
  },

  products: {
    title: 'Our Products & Solutions',
    subtitle: 'Comprehensive tools and frameworks for modern web development',
    framework: {
      title: 'Frontend Framework',
      description: 'Complete Next.js 15 based development framework',
      features: [
        'Next.js 15 App Router',
        'React 19 with latest features',
        'TypeScript strict mode',
        'Tailwind CSS integration',
      ],
    },
    components: {
      title: 'UI Component Library',
      description: 'Beautiful and accessible components built with Radix UI',
      features: [
        'shadcn/ui components',
        'Dark mode support',
        'Responsive design',
        'Accessibility first',
      ],
    },
    tools: {
      title: 'Development Tools',
      description: 'Complete toolchain for modern development workflow',
      features: [
        'Vitest testing framework',
        'ESLint + Prettier',
        'Husky Git hooks',
        'TypeScript support',
      ],
    },
    pricing: {
      free: 'Free',
      getStarted: 'Get Started',
      learnMore: 'Learn More',
    },
    badges: {
      popular: 'Popular',
      recommended: 'Recommended',
      new: 'New',
    },
  },

  blog: {
    title: 'Blog & Insights',
    subtitle: 'Latest news, tutorials, and insights from our team',
    readMore: 'Read More',
    publishedOn: 'Published on',
    author: 'Author',
    tags: 'Tags',
    categories: 'Categories',
    relatedPosts: 'Related Posts',
    noPostsFound: 'No posts found',
  },

  about: {
    title: 'About Us',
    subtitle: 'Learn more about our mission and team',
    mission: 'Our Mission',
    vision: 'Our Vision',
    values: 'Our Values',
    team: 'Our Team',
    history: 'Our History',
    contact: 'Contact Us',
  },

  forms: {
    validation: {
      required: 'This field is required',
      email: 'Please enter a valid email address',
      minLength: 'Minimum length is {min} characters',
      maxLength: 'Maximum length is {max} characters',
      pattern: 'Please enter a valid format',
    },
    contact: {
      name: 'Name',
      email: 'Email',
      subject: 'Subject',
      message: 'Message',
      submit: 'Send Message',
      success: 'Message sent successfully!',
      error: 'Failed to send message. Please try again.',
    },
  },

  meta: {
    defaultTitle: 'Tucsenberg Web 1.0',
    defaultDescription:
      'Modern frontend technology solution - Based on Next.js 15 and React 19',
    keywords: [
      'Next.js',
      'React',
      'TypeScript',
      'Tailwind CSS',
      'shadcn/ui',
      'Frontend',
      'Web Development',
    ],
  },
};

// 中文翻译
export const zhTranslations: Translations = {
  common: {
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    save: '保存',
    edit: '编辑',
    delete: '删除',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    close: '关闭',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    more: '更多',
    less: '收起',
  },

  navigation: {
    home: '首页',
    products: '产品',
    blog: '博客',
    about: '关于',
    contact: '联系',
    login: '登录',
    logout: '退出',
    profile: '个人资料',
    settings: '设置',
  },

  home: {
    title: 'Tucsenberg Web 1.0',
    subtitle:
      '现代化前端技术方案 - 基于 Next.js 15 和 React 19 的企业级开发框架',
    getStarted: '开始使用',
    learnMore: '了解更多',
    toggleTheme: '切换主题',
    configComplete: '技术栈配置完成 ✅',
    configDescription: '项目已成功配置以下核心功能',
    features: {
      nextjs: {
        title: 'Next.js 15 + React 19',
        description: '最新的 React 和 Next.js 技术栈',
      },
      ui: {
        title: 'shadcn/ui + Tailwind CSS',
        description: '现代化的设计系统和样式框架',
      },
      state: {
        title: 'Zustand + React Query',
        description: '高效的状态管理和数据获取',
      },
      typescript: {
        title: 'TypeScript + ESLint',
        description: '类型安全和代码质量保证',
      },
      testing: {
        title: 'Vitest + Testing Library',
        description: '完整的测试解决方案',
      },
      ai: {
        title: 'AI 反模式防护',
        description: '避免 AI 生成的常见错误',
      },
    },
    checklist: [
      'Next.js 15 + React 19',
      'TypeScript 配置',
      'Tailwind CSS + shadcn/ui',
      'Zustand 状态管理',
      'React Query 数据获取',
      'Vitest 测试框架',
      'ESLint + Prettier',
      'Husky Git Hooks',
    ],
  },

  products: {
    title: '我们的产品与解决方案',
    subtitle: '为现代 Web 开发提供全面的工具和框架',
    framework: {
      title: '前端开发框架',
      description: '基于 Next.js 15 的完整开发框架',
      features: [
        'Next.js 15 App Router',
        'React 19 最新特性',
        'TypeScript 严格模式',
        'Tailwind CSS 集成',
      ],
    },
    components: {
      title: 'UI 组件库',
      description: '基于 Radix UI 构建的美观且易用的组件',
      features: ['shadcn/ui 组件', '深色模式支持', '响应式设计', '无障碍优先'],
    },
    tools: {
      title: '开发工具',
      description: '现代开发工作流的完整工具链',
      features: [
        'Vitest 测试框架',
        'ESLint + Prettier',
        'Husky Git hooks',
        'TypeScript 支持',
      ],
    },
    pricing: {
      free: '免费',
      getStarted: '开始使用',
      learnMore: '了解更多',
    },
    badges: {
      popular: '热门',
      recommended: '推荐',
      new: '新品',
    },
  },

  blog: {
    title: '博客与见解',
    subtitle: '来自我们团队的最新资讯、教程和见解',
    readMore: '阅读更多',
    publishedOn: '发布于',
    author: '作者',
    tags: '标签',
    categories: '分类',
    relatedPosts: '相关文章',
    noPostsFound: '未找到文章',
  },

  about: {
    title: '关于我们',
    subtitle: '了解更多关于我们的使命和团队',
    mission: '我们的使命',
    vision: '我们的愿景',
    values: '我们的价值观',
    team: '我们的团队',
    history: '我们的历史',
    contact: '联系我们',
  },

  forms: {
    validation: {
      required: '此字段为必填项',
      email: '请输入有效的邮箱地址',
      minLength: '最少需要 {min} 个字符',
      maxLength: '最多允许 {max} 个字符',
      pattern: '请输入有效格式',
    },
    contact: {
      name: '姓名',
      email: '邮箱',
      subject: '主题',
      message: '消息',
      submit: '发送消息',
      success: '消息发送成功！',
      error: '发送失败，请重试。',
    },
  },

  meta: {
    defaultTitle: 'Tucsenberg Web 1.0',
    defaultDescription:
      '现代化前端技术方案 - 基于 Next.js 15 和 React 19 的企业级开发框架',
    keywords: [
      'Next.js',
      'React',
      'TypeScript',
      'Tailwind CSS',
      'shadcn/ui',
      '前端',
      'Web开发',
    ],
  },
};

// 翻译映射
export const translations: Record<Locale, Translations> = {
  en: enTranslations,
  zh: zhTranslations,
};
