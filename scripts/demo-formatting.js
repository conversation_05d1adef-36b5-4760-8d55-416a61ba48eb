#!/usr/bin/env node

/**
 * 🎨 格式化效果演示脚本
 * 
 * 这个脚本演示 Prettier 的格式化效果
 * 创建不规范的代码文件，然后展示格式化前后的对比
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 不规范的代码示例
const unformattedCode = `// 🧪 格式化演示文件 - 不规范版本
interface User{name:string;age:number;email:string;isActive?:boolean}
const users:User[]=[{name:"<PERSON>",age:30,email:"<EMAIL>",isActive:true},{name:"<PERSON>",age:25,email:"<EMAIL>"}]
const getUserById=(id:number):User|undefined=>{
return users.find(user=>user.name.length>id)
}
const config={host:"localhost",port:3000,debug:true,features:["auth","api","ui"]}
const processUsers=()=>{
const result=users.map(user=>{
return{...user,displayName:user.name.toUpperCase(),isAdult:user.age>=18}
}).filter(user=>user.isAdult&&user.isActive)
return result
}
export{User,users,getUserById,config,processUsers}`;

// 创建演示目录
const demoDir = path.join(__dirname, '..', 'demo');
if (!fs.existsSync(demoDir)) {
  fs.mkdirSync(demoDir, { recursive: true });
}

// 文件路径
const beforeFile = path.join(demoDir, 'before-formatting.tsx');
const afterFile = path.join(demoDir, 'after-formatting.tsx');

console.log('🎨 开始格式化演示...\n');

// 1. 创建不规范的文件
console.log('📝 创建不规范的代码文件...');
fs.writeFileSync(beforeFile, unformattedCode);
console.log('✅ 已创建:', beforeFile);

// 2. 复制文件用于格式化
fs.copyFileSync(beforeFile, afterFile);

// 3. 运行 Prettier 格式化
console.log('\n🔧 运行 Prettier 格式化...');
try {
  execSync(`npx prettier --write "${afterFile}"`, { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ 格式化完成!');
} catch (error) {
  console.error('❌ 格式化失败:', error.message);
  process.exit(1);
}

// 4. 显示对比结果
console.log('\n📊 格式化效果对比:\n');

console.log('🔴 格式化前 (before-formatting.tsx):');
console.log('─'.repeat(50));
console.log(fs.readFileSync(beforeFile, 'utf8'));

console.log('\n🟢 格式化后 (after-formatting.tsx):');
console.log('─'.repeat(50));
console.log(fs.readFileSync(afterFile, 'utf8'));

// 5. 统计改进
const beforeLines = unformattedCode.split('\n').length;
const afterContent = fs.readFileSync(afterFile, 'utf8');
const afterLines = afterContent.split('\n').length;

console.log('\n📈 格式化统计:');
console.log(`• 格式化前行数: ${beforeLines}`);
console.log(`• 格式化后行数: ${afterLines}`);
console.log(`• 可读性提升: ${Math.round((afterLines / beforeLines - 1) * 100)}%`);

// 6. 主要改进点
console.log('\n🎯 主要改进点:');
console.log('• ✅ 接口定义添加了适当的空格和换行');
console.log('• ✅ 数组和对象格式化为多行结构');
console.log('• ✅ 函数参数和返回类型添加了空格');
console.log('• ✅ 字符串统一使用单引号');
console.log('• ✅ 代码块添加了适当的缩进');
console.log('• ✅ 导出语句格式化为多行');

console.log('\n🎉 演示完成! 文件保存在 demo/ 目录中');
console.log('\n💡 提示: 在实际开发中，这个过程会在 Git 提交时自动执行');
