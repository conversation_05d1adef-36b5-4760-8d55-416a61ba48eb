#!/usr/bin/env tsx

/**
 * 自动格式化管理工具
 * 用于安全地格式化文件，避免意外修改重要配置
 */

import { execSync } from 'child_process';
import { existsSync, readFileSync } from 'fs';

interface FormatOptions {
  dryRun?: boolean;
  verbose?: boolean;
  includeConfig?: boolean;
  filePattern?: string;
}

class FormatManager {
  private protectedFiles = [
    'i18n.json',
    'components.json',
    'tsconfig.json',
    'package.json',
    'next.config.ts',
    'tailwind.config.ts',
    'vitest.config.ts',
    'eslint.config.mjs',
  ];

  private protectedPatterns = [
    '*.config.js',
    '*.config.ts',
    '*.config.mjs',
    '.env*',
    '*.lock',

  ];

  /**
   * 安全格式化 - 只格式化代码文件
   */
  async formatSafely(options: FormatOptions = {}) {
    console.log('🎨 开始安全格式化...\n');

    const patterns = [
      'src/**/*.{ts,tsx,js,jsx}',
      '__tests__/**/*.{ts,tsx,js,jsx}',
      'scripts/**/*.{ts,js}',
      '*.{css,scss}',
    ];

    if (options.includeConfig) {
      console.log('⚠️  包含配置文件格式化（谨慎模式）');
      patterns.push('*.{json,md}');
    }

    for (const pattern of patterns) {
      await this.formatPattern(pattern, options);
    }

    console.log('\n✅ 安全格式化完成！');
  }

  /**
   * 格式化特定模式的文件
   */
  private async formatPattern(pattern: string, options: FormatOptions) {
    const command = options.dryRun
      ? `npx prettier --check "${pattern}"`
      : `npx prettier --write "${pattern}"`;

    try {
      if (options.verbose) {
        console.log(`🔍 处理模式: ${pattern}`);
      }

      const result = execSync(command, {
        encoding: 'utf-8',
        stdio: options.verbose ? 'inherit' : 'pipe',
      });

      if (options.dryRun && result) {
        console.log(`📝 需要格式化: ${pattern}`);
      } else if (!options.dryRun) {
        console.log(`✅ 已格式化: ${pattern}`);
      }
    } catch (error: unknown) {
      if (options.dryRun) {
        console.log(`❌ 格式不正确: ${pattern}`);
      } else {
        const message = error instanceof Error ? error.message : String(error);
        console.log(`⚠️  格式化失败: ${pattern} - ${message}`);
      }
    }
  }

  /**
   * 检查文件是否受保护
   */
  isProtectedFile(filePath: string): boolean {
    const fileName = filePath.split('/').pop() || '';

    // 检查受保护的文件名
    if (this.protectedFiles.includes(fileName)) {
      return true;
    }

    // 检查受保护的模式
    return this.protectedPatterns.some((pattern) => {
      const regex = new RegExp(pattern.replace('*', '.*'));
      return regex.test(fileName);
    });
  }

  /**
   * 备份重要配置文件
   */
  async backupConfigs() {
    console.log('💾 备份重要配置文件...\n');

    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const backupDir = `backups/config-${timestamp}`;

    try {
      execSync(`mkdir -p ${backupDir}`);

      for (const file of this.protectedFiles) {
        if (existsSync(file)) {
          execSync(`cp ${file} ${backupDir}/`);
          console.log(`✅ 已备份: ${file}`);
        }
      }

      console.log(`\n📁 备份保存在: ${backupDir}`);
    } catch (error) {
      console.error('❌ 备份失败:', error);
    }
  }

  /**
   * 验证格式化配置
   */
  async validateConfig() {
    console.log('🔍 验证格式化配置...\n');

    // 检查 .prettierignore
    if (existsSync('.prettierignore')) {
      const ignoreContent = readFileSync('.prettierignore', 'utf-8');
      const hasConfigProtection = this.protectedFiles.some((file) =>
        ignoreContent.includes(file)
      );

      if (hasConfigProtection) {
        console.log('✅ .prettierignore 包含配置文件保护');
      } else {
        console.log('⚠️  .prettierignore 缺少配置文件保护');
      }
    }

    // 检查 lint-staged 配置
    if (existsSync('package.json')) {
      const packageJson = JSON.parse(readFileSync('package.json', 'utf-8'));
      const lintStaged = packageJson['lint-staged'];

      if (lintStaged) {
        const hasJsonPattern = Object.keys(lintStaged).some((pattern) =>
          pattern.includes('json')
        );

        if (hasJsonPattern) {
          console.log('⚠️  lint-staged 仍包含 JSON 文件格式化');
        } else {
          console.log('✅ lint-staged 配置安全');
        }
      }
    }
  }

  /**
   * 紧急恢复 - 从 git 恢复文件
   */
  async emergencyRestore(filePath: string) {
    console.log(`🚨 紧急恢复文件: ${filePath}`);

    try {
      execSync(`git checkout HEAD -- ${filePath}`);
      console.log(`✅ 已从 git 恢复: ${filePath}`);
    } catch (error) {
      console.error(`❌ 恢复失败: ${filePath}`, error);
    }
  }
}

// CLI 接口
async function main() {
  const args = process.argv.slice(2);
  const manager = new FormatManager();

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🎨 格式化管理工具

用法:
  npm run format:safe              # 安全格式化（推荐）
  npm run format:safe -- --dry-run # 检查格式但不修改
  npm run format:safe -- --verbose # 详细输出
  npm run format:backup            # 备份配置文件
  npm run format:validate          # 验证配置
  npm run format:restore <file>    # 紧急恢复文件

选项:
  --dry-run    只检查，不修改文件
  --verbose    显示详细输出
  --include-config  包含配置文件（谨慎使用）
    `);
    return;
  }

  if (args.includes('backup')) {
    await manager.backupConfigs();
  } else if (args.includes('validate')) {
    await manager.validateConfig();
  } else if (args.includes('restore')) {
    const file = args[args.indexOf('restore') + 1];
    if (file) {
      await manager.emergencyRestore(file);
    } else {
      console.error('❌ 请指定要恢复的文件');
    }
  } else {
    const options: FormatOptions = {
      dryRun: args.includes('--dry-run'),
      verbose: args.includes('--verbose'),
      includeConfig: args.includes('--include-config'),
    };

    await manager.formatSafely(options);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { FormatManager };
