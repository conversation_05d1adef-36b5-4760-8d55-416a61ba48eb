// 🧪 实际格式化测试文件 - 不规范版本
// 这个文件故意写成不规范的格式来测试工具链

interface TestUser{id:number;name:string;email:string;isActive?:boolean}

const testUsers:TestUser[]=[{id:1,name:"Test User",email:"<EMAIL>",isActive:true},{id:2,name:"Another User",email:"<EMAIL>"}]

const processTestData=()=>{
return testUsers.map(user=>({...user,processed:true,timestamp:Date.now()})).filter(user=>user.isActive)
}

const validateUser=(user:TestUser):boolean=>{
if(user.id>0&&user.name.length>0&&user.email.includes("@")){
return true
}else{
return false
}
}

export type{TestUser}
export{testUsers,processTestData,validateUser}
