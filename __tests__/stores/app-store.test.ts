import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { useAppStore, type Theme, type User } from '@/stores/app-store';

// Mock localStorage for persist middleware
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

describe('App Store', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });
    
    // Reset store state
    useAppStore.setState({
      theme: 'system',
      user: null,
      sidebarOpen: false,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      const state = useAppStore.getState();
      
      expect(state.theme).toBe('system');
      expect(state.user).toBe(null);
      expect(state.sidebarOpen).toBe(false);
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('Theme Management', () => {
    it('should set theme to light', () => {
      const { setTheme } = useAppStore.getState();
      
      setTheme('light');
      
      expect(useAppStore.getState().theme).toBe('light');
    });

    it('should set theme to dark', () => {
      const { setTheme } = useAppStore.getState();
      
      setTheme('dark');
      
      expect(useAppStore.getState().theme).toBe('dark');
    });

    it('should set theme to system', () => {
      const { setTheme } = useAppStore.getState();
      
      setTheme('system');
      
      expect(useAppStore.getState().theme).toBe('system');
    });

    it('should handle all valid theme values', () => {
      const { setTheme } = useAppStore.getState();
      const themes: Theme[] = ['light', 'dark', 'system'];
      
      themes.forEach(theme => {
        setTheme(theme);
        expect(useAppStore.getState().theme).toBe(theme);
      });
    });
  });

  describe('User Management', () => {
    const mockUser: User = {
      id: '123',
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: 'https://example.com/avatar.jpg',
    };

    it('should set user', () => {
      const { setUser } = useAppStore.getState();
      
      setUser(mockUser);
      
      const state = useAppStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
    });

    it('should clear user', () => {
      const { setUser } = useAppStore.getState();
      
      // First set a user
      setUser(mockUser);
      expect(useAppStore.getState().isAuthenticated).toBe(true);
      
      // Then clear the user
      setUser(null);
      
      const state = useAppStore.getState();
      expect(state.user).toBe(null);
      expect(state.isAuthenticated).toBe(false);
    });

    it('should handle user without avatar', () => {
      const { setUser } = useAppStore.getState();
      const userWithoutAvatar: User = {
        id: '456',
        name: 'Jane Doe',
        email: '<EMAIL>',
      };
      
      setUser(userWithoutAvatar);
      
      expect(useAppStore.getState().user).toEqual(userWithoutAvatar);
    });

    it('should logout user', () => {
      const { setUser, logout } = useAppStore.getState();
      
      // First set a user
      setUser(mockUser);
      expect(useAppStore.getState().isAuthenticated).toBe(true);
      
      // Then logout
      logout();
      
      const state = useAppStore.getState();
      expect(state.user).toBe(null);
      expect(state.isAuthenticated).toBe(false);
    });
  });

  describe('Sidebar Management', () => {
    it('should set sidebar open', () => {
      const { setSidebarOpen } = useAppStore.getState();
      
      setSidebarOpen(true);
      
      expect(useAppStore.getState().sidebarOpen).toBe(true);
    });

    it('should set sidebar closed', () => {
      const { setSidebarOpen } = useAppStore.getState();
      
      setSidebarOpen(false);
      
      expect(useAppStore.getState().sidebarOpen).toBe(false);
    });

    it('should toggle sidebar from closed to open', () => {
      const { toggleSidebar } = useAppStore.getState();
      
      // Initial state is closed
      expect(useAppStore.getState().sidebarOpen).toBe(false);
      
      toggleSidebar();
      
      expect(useAppStore.getState().sidebarOpen).toBe(true);
    });

    it('should toggle sidebar from open to closed', () => {
      const { setSidebarOpen, toggleSidebar } = useAppStore.getState();
      
      // First set to open
      setSidebarOpen(true);
      expect(useAppStore.getState().sidebarOpen).toBe(true);
      
      // Then toggle
      toggleSidebar();
      
      expect(useAppStore.getState().sidebarOpen).toBe(false);
    });

    it('should toggle sidebar multiple times', () => {
      const { toggleSidebar } = useAppStore.getState();
      
      // Initial state is closed
      expect(useAppStore.getState().sidebarOpen).toBe(false);
      
      // Toggle to open
      toggleSidebar();
      expect(useAppStore.getState().sidebarOpen).toBe(true);
      
      // Toggle to closed
      toggleSidebar();
      expect(useAppStore.getState().sidebarOpen).toBe(false);
      
      // Toggle to open again
      toggleSidebar();
      expect(useAppStore.getState().sidebarOpen).toBe(true);
    });
  });

  describe('Computed Properties', () => {
    it('should return false for isAuthenticated when user is null', () => {
      const state = useAppStore.getState();
      
      expect(state.user).toBe(null);
      expect(state.isAuthenticated).toBe(false);
    });

    it('should return true for isAuthenticated when user is set', () => {
      const { setUser } = useAppStore.getState();
      const mockUser: User = {
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
      };
      
      setUser(mockUser);
      
      const state = useAppStore.getState();
      expect(state.user).toEqual(mockUser);
      expect(state.isAuthenticated).toBe(true);
    });

    it('should update isAuthenticated when user changes', () => {
      const { setUser } = useAppStore.getState();
      const mockUser: User = {
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
      };
      
      // Initially not authenticated
      expect(useAppStore.getState().isAuthenticated).toBe(false);
      
      // Set user - should be authenticated
      setUser(mockUser);
      expect(useAppStore.getState().isAuthenticated).toBe(true);
      
      // Clear user - should not be authenticated
      setUser(null);
      expect(useAppStore.getState().isAuthenticated).toBe(false);
    });
  });

  describe('State Persistence', () => {
    it('should persist theme and user data', () => {
      const { setTheme, setUser } = useAppStore.getState();
      const mockUser: User = {
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
      };
      
      setTheme('dark');
      setUser(mockUser);
      
      // The persist middleware should save to localStorage
      // Note: In a real test environment, you might need to trigger the persistence manually
      // or wait for the debounced save operation
    });

    it('should not persist sidebar state', () => {
      const { setSidebarOpen } = useAppStore.getState();
      
      setSidebarOpen(true);
      
      // Sidebar state should not be included in persistence
      // This is verified by the partialize function in the store configuration
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete user workflow', () => {
      const { setUser, setTheme, setSidebarOpen, logout } = useAppStore.getState();
      const mockUser: User = {
        id: '123',
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
      };
      
      // Initial state
      expect(useAppStore.getState().isAuthenticated).toBe(false);
      expect(useAppStore.getState().theme).toBe('system');
      expect(useAppStore.getState().sidebarOpen).toBe(false);
      
      // User logs in and sets preferences
      setUser(mockUser);
      setTheme('dark');
      setSidebarOpen(true);
      
      let state = useAppStore.getState();
      expect(state.isAuthenticated).toBe(true);
      expect(state.user).toEqual(mockUser);
      expect(state.theme).toBe('dark');
      expect(state.sidebarOpen).toBe(true);
      
      // User logs out
      logout();
      
      state = useAppStore.getState();
      expect(state.isAuthenticated).toBe(false);
      expect(state.user).toBe(null);
      expect(state.theme).toBe('dark'); // Theme should persist
      expect(state.sidebarOpen).toBe(true); // Sidebar state should remain
    });

    it('should handle multiple theme changes', () => {
      const { setTheme } = useAppStore.getState();
      
      setTheme('light');
      expect(useAppStore.getState().theme).toBe('light');
      
      setTheme('dark');
      expect(useAppStore.getState().theme).toBe('dark');
      
      setTheme('system');
      expect(useAppStore.getState().theme).toBe('system');
    });
  });
});
