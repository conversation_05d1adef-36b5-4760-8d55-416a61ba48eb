import Link from 'next/link';
import { type Locale } from '@/lib/i18n/config';
import { getLocalizedHref } from '@/lib/i18n/navigation';

interface FooterProps {
  locale: Locale;
}

export function Footer({ locale }: FooterProps) {
  const content = {
    en: {
      company: '<PERSON>csenberg',
      description: 'Professional flood protection solutions',
      rights: 'All rights reserved.',
      links: {
        products: 'Products',
        about: 'About',
        blog: 'Blog',
        contact: 'Contact',
      },
    },
    zh: {
      company: 'Tucsenberg',
      description: '专业防洪保护解决方案',
      rights: '版权所有。',
      links: {
        products: '产品',
        about: '关于',
        blog: '博客',
        contact: '联系',
      },
    },
  };

  const currentContent = content[locale];
  const currentYear = new Date().getFullYear();

  return (
    <footer className="mt-auto border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          {/* Logo and Description */}
          <div className="flex flex-col items-center md:items-start text-center md:text-left">
            <Link
              href={getLocalizedHref('/', locale)}
              className="flex items-center space-x-2 mb-2"
            >
              <div className="h-6 w-6 rounded-md bg-gradient-to-r from-blue-600 to-purple-600" />
              <span className="font-bold text-lg">
                {currentContent.company}
              </span>
            </Link>
            <p className="text-sm text-muted-foreground max-w-xs">
              {currentContent.description}
            </p>
          </div>

          {/* Navigation Links */}
          <div className="flex items-center gap-6">
            <Link
              href={getLocalizedHref('/products', locale)}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {currentContent.links.products}
            </Link>
            <Link
              href={getLocalizedHref('/about', locale)}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {currentContent.links.about}
            </Link>
            <Link
              href={getLocalizedHref('/blog', locale)}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {currentContent.links.blog}
            </Link>
            <Link
              href={getLocalizedHref('/contact', locale)}
              className="text-sm text-muted-foreground hover:text-foreground transition-colors"
            >
              {currentContent.links.contact}
            </Link>
          </div>

          {/* Copyright */}
          <div className="text-center md:text-right">
            <p className="text-sm text-muted-foreground">
              © {currentYear} {currentContent.company}. {currentContent.rights}
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
