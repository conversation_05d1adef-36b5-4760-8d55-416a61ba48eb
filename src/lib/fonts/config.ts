import {
  Geist,
  Not<PERSON>_Sans_SC,
  Noto_Sans_JP,
  Noto_Sans_KR,
} from 'next/font/google';
import { type Locale } from '@/lib/i18n/config';

// 主要字体 - Geist Variable (现代科技感，Vercel 设计)
// 评分: 92/100 - 现代几何美学，优秀品牌识别度，科技感强
export const geist = Geist({
  subsets: ['latin', 'latin-ext'],
  variable: '--font-geist',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700', '800'],
  preload: true,
  fallback: [
    'SF Pro Display', // Apple 系统字体，优秀的屏幕表现
    'system-ui', // 系统默认无衬线字体
    '-apple-system', // macOS/iOS 系统字体
    'BlinkMacSystemFont', // Chrome on macOS
    'Segoe UI', // Windows 系统字体
    'Helvetica Neue', // 经典现代字体
    'Arial', // 通用回退
    'sans-serif', // 最终回退
  ],
});

// 显示字体 - 用于标题和品牌元素
// 使用 Geist 的 Display 变体，针对大字号优化，现代科技感
export const geistDisplay = Geist({
  subsets: ['latin', 'latin-ext'],
  variable: '--font-geist-display',
  display: 'swap',
  weight: ['500', '600', '700', '800', '900'],
  preload: true,
  fallback: [
    'SF Pro Display', // Apple 显示字体
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
  ],
});

// 简体中文字体 - 苹方为主，优秀的屏幕显示效果
// 注意：优先使用系统字体，不需要额外加载，性能最优
export const chineseSimplified = {
  variable: '--font-chinese-sc',
  className: 'font-chinese-sc',
  fallback: [
    'PingFang SC', // 苹果设计，阅读舒适度高，主选择
    'Source Han Sans SC', // Adobe/Google 联合开发，专业品质
    'Microsoft YaHei', // Windows 标准，兼容性好
    'Hiragino Sans GB', // 日本字游工房设计，优雅现代
    'WenQuanYi Micro Hei', // 开源选择
    'sans-serif',
  ],
};

// 备用：Noto Sans SC (仅在需要特殊字符支持时使用)
export const notoSansSC = Noto_Sans_SC({
  subsets: ['latin'],
  variable: '--font-noto-sc',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
  preload: false, // 按需加载以优化性能
  fallback: [
    'PingFang SC', // 苹果设计，阅读舒适度高，主选择
    'Source Han Sans SC', // Adobe/Google 联合开发，专业品质
    'Microsoft YaHei', // Windows 标准，兼容性好
    'Hiragino Sans GB', // 日本字游工房设计，优雅现代
    'WenQuanYi Micro Hei', // 开源选择
    'sans-serif',
  ],
});

// 日语字体
export const notoSansJP = Noto_Sans_JP({
  subsets: ['latin'],
  variable: '--font-noto-jp',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
  preload: false,
  fallback: [
    'Hiragino Kaku Gothic ProN',
    'Hiragino Sans',
    'Yu Gothic',
    'Meiryo',
    'Source Han Sans JP',
    'sans-serif',
  ],
});

// 韩语字体
export const notoSansKR = Noto_Sans_KR({
  subsets: ['latin'],
  variable: '--font-noto-kr',
  display: 'swap',
  weight: ['300', '400', '500', '600', '700'],
  preload: false,
  fallback: [
    'Apple SD Gothic Neo',
    'Malgun Gothic',
    'Nanum Gothic',
    'Source Han Sans KR',
    'sans-serif',
  ],
});

// 字体映射配置 - 基于美观性和性能优化
export const FONT_CONFIG = {
  // 拉丁字符系语言 - 使用 Geist (92/100 评分)
  en: {
    font: geist,
    displayFont: geistDisplay,
    className: 'font-geist',
    displayClassName: 'font-geist-display',
    cssVariable: '--font-geist',
    displayVariable: '--font-geist-display',
    preload: true,
    subset: 'latin',
    description: '英语 - 现代科技感，优秀的屏幕阅读体验',
  },
  fr: {
    font: geist,
    displayFont: geistDisplay,
    className: 'font-geist',
    displayClassName: 'font-geist-display',
    cssVariable: '--font-geist',
    displayVariable: '--font-geist-display',
    preload: false,
    subset: 'latin-ext',
    description: '法语 - 完整的拉丁扩展字符支持',
  },
  de: {
    font: geist,
    displayFont: geistDisplay,
    className: 'font-geist',
    displayClassName: 'font-geist-display',
    cssVariable: '--font-geist',
    displayVariable: '--font-geist-display',
    preload: false,
    subset: 'latin-ext',
    description: '德语 - 优秀的德语特殊字符支持',
  },
  es: {
    font: geist,
    displayFont: geistDisplay,
    className: 'font-geist',
    displayClassName: 'font-geist-display',
    cssVariable: '--font-geist',
    displayVariable: '--font-geist-display',
    preload: false,
    subset: 'latin-ext',
    description: '西班牙语 - 完整的西语字符集',
  },

  // CJK 语言
  zh: {
    font: null, // 使用系统字体，不需要 Next.js 字体加载器
    className: 'font-chinese-sc',
    cssVariable: '--font-chinese-sc',
    preload: false, // 系统字体，无需预加载
    subset: 'chinese-simplified',
    description: '中文 - 苹方为主，优秀的屏幕显示效果',
  },
  'zh-CN': {
    font: notoSansSC,
    className: 'font-noto-sc',
    cssVariable: '--font-noto-sc',
    preload: false,
    subset: 'chinese-simplified',
  },
  ja: {
    font: notoSansJP,
    className: 'font-noto-jp',
    cssVariable: '--font-noto-jp',
    preload: false,
    subset: 'japanese',
  },
  ko: {
    font: notoSansKR,
    className: 'font-noto-kr',
    cssVariable: '--font-noto-kr',
    preload: false,
    subset: 'korean',
  },
} as const;

// 获取语言对应的字体配置
export function getFontConfig(locale: Locale) {
  return FONT_CONFIG[locale] || FONT_CONFIG.en;
}

// 获取字体类名
export function getFontClassName(locale: Locale): string {
  const config = getFontConfig(locale);
  return config.className;
}

// 获取字体样式
export function getFontStyle(locale: Locale): string {
  // 使用字体栈而不是配置中的 fallback
  return getFontStack(locale);
}

// 获取所有字体的 CSS 变量
export function getAllFontVariables(): string {
  return [
    geist.variable,
    geistDisplay.variable,
    // 注意：中文使用系统字体，不需要包含在这里
    // notoSansSC.variable, // 仅在需要时使用
    // notoSansJP.variable, // 仅在需要时使用
    // notoSansKR.variable, // 仅在需要时使用
  ].join(' ');
}

// 字体预加载配置
export function getFontPreloadConfig(locale: Locale) {
  const config = getFontConfig(locale);

  if (!config.preload || !config.font) {
    return null;
  }

  // Next.js 字体会自动处理预加载，我们不需要手动指定路径
  // 这个函数保留用于未来可能的自定义预加载需求
  return null;
}

// 字体回退栈
export const FONT_STACKS = {
  latin: [
    'var(--font-geist)',
    'SF Pro Display',
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Helvetica Neue',
    'Arial',
    'sans-serif',
  ],
  'chinese-simplified': [
    'PingFang SC',
    'Source Han Sans SC',
    'Microsoft YaHei',
    'Hiragino Sans GB',
    'WenQuanYi Micro Hei',
    'sans-serif',
  ],
  japanese: [
    'var(--font-noto-jp)',
    'Hiragino Kaku Gothic ProN',
    'Hiragino Sans',
    'Yu Gothic',
    'Meiryo',
    'Source Han Sans JP',
    'sans-serif',
  ],
  korean: [
    'var(--font-noto-kr)',
    'Apple SD Gothic Neo',
    'Malgun Gothic',
    'Nanum Gothic',
    'Source Han Sans KR',
    'sans-serif',
  ],
} as const;

// 获取字体栈
export function getFontStack(locale: Locale): string {
  const stackMap: Record<string, keyof typeof FONT_STACKS> = {
    en: 'latin',
    fr: 'latin',
    de: 'latin',
    es: 'latin',
    zh: 'chinese-simplified',
    'zh-CN': 'chinese-simplified',
    ja: 'japanese',
    ko: 'korean',
  };

  const stackKey = stackMap[locale] || 'latin';
  return FONT_STACKS[stackKey].join(', ');
}

// 字体性能优化配置
export const FONT_PERFORMANCE_CONFIG = {
  // 字体加载超时时间 (毫秒)
  loadTimeout: 3000,

  // 是否启用字体缓存
  enableCache: true,

  // 缓存持续时间 (毫秒)
  cacheDuration: 30 * 24 * 60 * 60 * 1000, // 30 天

  // 是否启用字体加载监控
  enableMonitoring: process.env.NODE_ENV === 'production',

  // 字体子集化配置
  subsetConfig: {
    'chinese-simplified': {
      // 常用汉字范围
      unicodeRange: 'U+4E00-9FFF,U+3400-4DBF,U+F900-FAFF',
      // 基础字符集大小限制 (KB)
      maxSize: 500,
    },
    japanese: {
      unicodeRange: 'U+3040-309F,U+30A0-30FF,U+4E00-9FAF,U+FF00-FFEF',
      maxSize: 400,
    },
    korean: {
      unicodeRange: 'U+AC00-D7AF,U+1100-11FF,U+3130-318F,U+A960-A97F',
      maxSize: 300,
    },
  },
} as const;
