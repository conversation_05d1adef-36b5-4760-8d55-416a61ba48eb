#!/usr/bin/env tsx

/**
 * 配置文件监控工具
 * 监控重要配置文件的变化，防止意外修改
 */

import { readFileSync, writeFileSync, existsSync, watchFile } from 'fs';
import { createHash } from 'crypto';

interface FileSnapshot {
  path: string;
  hash: string;
  timestamp: number;
  content: string;
}

class ConfigFileMonitor {
  private snapshots: Map<string, FileSnapshot> = new Map();
  private protectedFiles = [
    'i18n.json',
    'components.json',
    'tsconfig.json',
    'next.config.ts',
    'tailwind.config.ts',
    'vitest.config.ts',
    'eslint.config.mjs',
  ];

  constructor() {
    this.initializeSnapshots();
  }

  /**
   * 初始化文件快照
   */
  private initializeSnapshots() {
    console.log('📸 初始化配置文件快照...\n');

    for (const file of this.protectedFiles) {
      if (existsSync(file)) {
        const snapshot = this.createSnapshot(file);
        this.snapshots.set(file, snapshot);
        console.log(`✅ 已创建快照: ${file}`);
      } else {
        console.log(`⚠️  文件不存在: ${file}`);
      }
    }

    console.log(`\n📊 共监控 ${this.snapshots.size} 个配置文件`);
  }

  /**
   * 创建文件快照
   */
  private createSnapshot(filePath: string): FileSnapshot {
    const content = readFileSync(filePath, 'utf-8');
    const hash = createHash('sha256').update(content).digest('hex');

    return {
      path: filePath,
      hash,
      timestamp: Date.now(),
      content,
    };
  }

  /**
   * 检查文件是否被修改
   */
  checkForChanges(): {
    changed: string[];
    details: Array<Record<string, unknown>>;
  } {
    const changed: string[] = [];
    const details: Array<Record<string, unknown>> = [];

    console.log('🔍 检查配置文件变化...\n');

    for (const [filePath, snapshot] of this.snapshots) {
      if (!existsSync(filePath)) {
        console.log(`❌ 文件已删除: ${filePath}`);
        changed.push(filePath);
        details.push({
          file: filePath,
          status: 'deleted',
          timestamp: new Date().toISOString(),
        });
        continue;
      }

      const currentSnapshot = this.createSnapshot(filePath);

      if (currentSnapshot.hash !== snapshot.hash) {
        console.log(`🚨 文件已修改: ${filePath}`);
        changed.push(filePath);

        // 分析具体变化
        const changes = this.analyzeChanges(snapshot, currentSnapshot);
        details.push({
          file: filePath,
          status: 'modified',
          timestamp: new Date().toISOString(),
          changes,
        });

        // 更新快照
        this.snapshots.set(filePath, currentSnapshot);
      } else {
        console.log(`✅ 文件未变化: ${filePath}`);
      }
    }

    return { changed, details };
  }

  /**
   * 分析文件变化详情
   */
  private analyzeChanges(oldSnapshot: FileSnapshot, newSnapshot: FileSnapshot) {
    const oldLines = oldSnapshot.content.split('\n');
    const newLines = newSnapshot.content.split('\n');

    const changes: Record<string, unknown> = {
      linesAdded: Math.max(0, newLines.length - oldLines.length),
      linesRemoved: Math.max(0, oldLines.length - newLines.length),
      sizeChange: newSnapshot.content.length - oldSnapshot.content.length,
      oldHash: oldSnapshot.hash.substring(0, 8),
      newHash: newSnapshot.hash.substring(0, 8),
    };

    // 特殊检查 i18n.json
    if (oldSnapshot.path === 'i18n.json') {
      try {
        const oldConfig = JSON.parse(oldSnapshot.content);
        const newConfig = JSON.parse(newSnapshot.content);

        changes['i18n'] = {
          providerChanged: oldConfig.provider?.id !== newConfig.provider?.id,
          targetsChanged:
            JSON.stringify(oldConfig.locale?.targets) !==
            JSON.stringify(newConfig.locale?.targets),
          bucketsChanged:
            JSON.stringify(oldConfig.buckets) !==
            JSON.stringify(newConfig.buckets),
          oldProvider: oldConfig.provider?.id || 'none',
          newProvider: newConfig.provider?.id || 'none',
          oldTargets: oldConfig.locale?.targets || [],
          newTargets: newConfig.locale?.targets || [],
        };
      } catch {
        changes['i18n'] = { parseError: true };
      }
    }

    return changes;
  }

  /**
   * 恢复文件到快照状态
   */
  restoreFile(filePath: string): boolean {
    const snapshot = this.snapshots.get(filePath);
    if (!snapshot) {
      console.error(`❌ 没有找到文件快照: ${filePath}`);
      return false;
    }

    try {
      writeFileSync(filePath, snapshot.content, 'utf-8');
      console.log(`✅ 已恢复文件: ${filePath}`);
      return true;
    } catch (error) {
      console.error(`❌ 恢复文件失败: ${filePath}`, error);
      return false;
    }
  }

  /**
   * 保存监控报告
   */
  saveReport(details: Array<Record<string, unknown>>) {
    const report = {
      timestamp: new Date().toISOString(),
      monitoredFiles: Array.from(this.snapshots.keys()),
      changes: details,
      summary: {
        totalFiles: this.snapshots.size,
        changedFiles: details.length,
        criticalChanges: details.filter(
          (d) =>
            d.file === 'i18n.json' &&
            (d.changes as Record<string, unknown>)?.i18n
        ),
      },
    };

    const reportPath = 'config-monitor-report.json';
    writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`📄 监控报告已保存: ${reportPath}`);
  }

  /**
   * 启动实时监控
   */
  startWatching() {
    console.log('👁️  启动实时文件监控...\n');

    for (const filePath of this.protectedFiles) {
      if (existsSync(filePath)) {
        watchFile(filePath, { interval: 1000 }, () => {
          console.log(`🔔 检测到文件变化: ${filePath}`);
          const { changed, details } = this.checkForChanges();

          if (changed.length > 0) {
            console.log(`\n🚨 发现 ${changed.length} 个文件被修改!`);
            this.saveReport(details);

            // 特殊处理 i18n.json
            if (changed.includes('i18n.json')) {
              console.log('\n⚠️  i18n.json 被修改，这可能影响翻译功能！');
              console.log('💡 运行 npm run config:restore i18n.json 来恢复');
            }
          }
        });
        console.log(`👀 正在监控: ${filePath}`);
      }
    }

    console.log('\n✅ 文件监控已启动，按 Ctrl+C 停止');
  }
}

// CLI 接口
async function main() {
  const args = process.argv.slice(2);
  const monitor = new ConfigFileMonitor();

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
📸 配置文件监控工具

用法:
  npm run config:check     # 检查文件变化
  npm run config:watch     # 启动实时监控
  npm run config:restore <file>  # 恢复文件

选项:
  --help, -h    显示帮助信息
    `);
    return;
  }

  if (args.includes('watch')) {
    monitor.startWatching();
    // 保持进程运行
    process.on('SIGINT', () => {
      console.log('\n👋 停止文件监控');
      process.exit(0);
    });
  } else if (args.includes('restore')) {
    const file = args[args.indexOf('restore') + 1];
    if (file) {
      monitor.restoreFile(file);
    } else {
      console.error('❌ 请指定要恢复的文件');
    }
  } else {
    const { changed, details } = monitor.checkForChanges();

    if (changed.length > 0) {
      console.log(`\n🚨 发现 ${changed.length} 个文件被修改!`);
      monitor.saveReport(details);
    } else {
      console.log('\n✅ 所有配置文件都未被修改');
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { ConfigFileMonitor };
