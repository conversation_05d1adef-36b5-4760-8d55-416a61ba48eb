/**
 * 安全工具函数
 * 
 * 提供各种安全相关的工具函数
 */

import { headers } from 'next/headers';
import { env } from '@/env';

/**
 * 生成安全的随机字符串
 */
export function generateSecureToken(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * 生成 nonce 用于 CSP
 */
export function generateNonce(): string {
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }
  
  // 降级方案
  return generateSecureToken(32);
}

/**
 * 清理用户输入，防止 XSS
 */
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // 移除尖括号
    .replace(/javascript:/gi, '') // 移除 javascript: 协议
    .replace(/on\w+=/gi, '') // 移除事件处理器
    .trim();
}

/**
 * 验证 URL 是否安全
 */
export function isSecureUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    
    // 只允许 http 和 https 协议
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      return false;
    }
    
    // 检查是否为本地地址或配置的可信域名
    const hostname = parsedUrl.hostname;
    
    // 本地地址
    if (['localhost', '127.0.0.1', '::1'].includes(hostname)) {
      return process.env.NODE_ENV === 'development';
    }
    
    // 检查是否为配置的应用域名
    try {
      const appUrl = new URL(env.NEXT_PUBLIC_APP_URL);
      return hostname === appUrl.hostname || 
             hostname.endsWith(`.${appUrl.hostname}`);
    } catch {
      return false;
    }
  } catch {
    return false;
  }
}

/**
 * 获取客户端 IP 地址
 */
export async function getClientIP(): Promise<string | null> {
  const headersList = await headers();
  
  // 按优先级检查各种可能的 IP 头部
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded',
  ];
  
  for (const header of ipHeaders) {
    const value = headersList.get(header);
    if (value) {
      // x-forwarded-for 可能包含多个 IP，取第一个
      const ip = value.split(',')[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }
  
  return null;
}

/**
 * 验证 IP 地址格式
 */
function isValidIP(ip: string): boolean {
  // IPv4 正则
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 正则（简化版）
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

/**
 * 检查请求是否来自爬虫
 */
export async function isBotRequest(): Promise<boolean> {
  const headersList = await headers();
  const userAgent = headersList.get('user-agent')?.toLowerCase() || '';
  
  const botPatterns = [
    'googlebot',
    'bingbot',
    'slurp',
    'duckduckbot',
    'baiduspider',
    'yandexbot',
    'facebookexternalhit',
    'twitterbot',
    'linkedinbot',
    'whatsapp',
    'telegrambot',
  ];
  
  return botPatterns.some(pattern => userAgent.includes(pattern));
}

/**
 * 生成 CSP nonce 并注入到头部
 */
export async function getCSPNonce(): Promise<string> {
  const headersList = await headers();
  let nonce = headersList.get('x-nonce');
  
  if (!nonce) {
    nonce = generateNonce();
  }
  
  return nonce;
}

/**
 * 验证 CSRF token
 */
export function validateCSRFToken(token: string, sessionToken: string): boolean {
  // 简单的 token 验证逻辑
  // 在实际应用中，应该使用更安全的验证方法
  return token === sessionToken && token.length >= 32;
}

/**
 * 创建安全的 cookie 选项
 */
export function getSecureCookieOptions() {
  const isProduction = process.env.NODE_ENV === 'production';
  
  return {
    httpOnly: true,
    secure: isProduction, // 生产环境强制 HTTPS
    sameSite: 'strict' as const,
    maxAge: 60 * 60 * 24 * 7, // 7 天
    path: '/',
  };
}

/**
 * 检查请求头是否包含可疑内容
 */
export async function detectSuspiciousHeaders(): Promise<string[]> {
  const headersList = await headers();
  const suspiciousPatterns = [
    /script/i,
    /javascript/i,
    /vbscript/i,
    /onload/i,
    /onerror/i,
    /<.*>/,
  ];
  
  const suspiciousHeaders: string[] = [];
  
  headersList.forEach((value, key) => {
    if (suspiciousPatterns.some(pattern => pattern.test(value))) {
      suspiciousHeaders.push(`${key}: ${value}`);
    }
  });
  
  return suspiciousHeaders;
}

/**
 * 安全的 JSON 解析
 */
export function safeJSONParse<T>(json: string, fallback: T): T {
  try {
    const parsed = JSON.parse(json);
    return parsed;
  } catch {
    return fallback;
  }
}

/**
 * 限制字符串长度，防止 DoS 攻击
 */
export function limitStringLength(str: string, maxLength: number = 1000): string {
  return str.length > maxLength ? str.substring(0, maxLength) : str;
}

/**
 * 验证文件类型是否安全
 */
export function isSecureFileType(filename: string): boolean {
  const allowedExtensions = [
    '.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg',
    '.pdf', '.txt', '.md', '.json',
    '.mp4', '.webm', '.mp3', '.wav',
  ];
  
  const extension = filename.toLowerCase().split('.').pop();
  return extension ? allowedExtensions.includes(`.${extension}`) : false;
}

/**
 * 生成安全报告
 */
export async function generateSecurityReport() {
  const headersList = await headers();
  const clientIP = await getClientIP();
  const isBot = await isBotRequest();
  const suspiciousHeaders = await detectSuspiciousHeaders();
  
  return {
    timestamp: new Date().toISOString(),
    clientIP,
    isBot,
    suspiciousHeaders,
    userAgent: headersList.get('user-agent'),
    origin: headersList.get('origin'),
    referer: headersList.get('referer'),
  };
}
