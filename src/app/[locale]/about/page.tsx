import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { type Locale } from '@/lib/i18n/config';
import { ContactButton } from '@/components/interactive';
import {
  Users,
  Target,
  Lightbulb,
  Heart,
  Github,
  Twitter,
  Linkedin,
  Award,
  Zap,
  Globe,
} from 'lucide-react';

interface AboutPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function AboutPage({ params }: AboutPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'About Tucsenberg',
      subtitle: 'Building the future of modern frontend development',
      mission: 'Our Mission',
      vision: 'Our Vision',
      values: 'Our Values',
      team: 'Our Team',
      contact: 'Get in Touch',
      missionText:
        'To provide developers with the most advanced, efficient, and enjoyable tools for building modern web applications. We believe in empowering creativity through technology.',
      visionText:
        'To become the leading platform for modern frontend development, setting new standards for developer experience and application performance.',
      valuesList: [
        {
          title: 'Innovation',
          description:
            "We constantly push the boundaries of what's possible in frontend development.",
          icon: Lightbulb,
        },
        {
          title: 'Quality',
          description:
            'We maintain the highest standards in code quality, performance, and user experience.',
          icon: Award,
        },
        {
          title: 'Community',
          description:
            'We believe in the power of open source and community-driven development.',
          icon: Users,
        },
        {
          title: 'Performance',
          description:
            'We optimize for speed, efficiency, and scalability in everything we build.',
          icon: Zap,
        },
      ],
      teamMembers: [
        {
          name: 'Alex Chen',
          role: 'Lead Frontend Architect',
          description:
            'Expert in React, Next.js, and modern frontend architecture with 8+ years of experience.',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
        {
          name: 'Sarah Johnson',
          role: 'UI/UX Designer',
          description:
            'Passionate about creating beautiful, accessible, and user-friendly interfaces.',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
        {
          name: 'Mike Rodriguez',
          role: 'DevOps Engineer',
          description:
            'Specializes in CI/CD, performance optimization, and scalable infrastructure.',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
      ],
      stats: [
        { label: 'Projects Delivered', value: '100+' },
        { label: 'Happy Clients', value: '50+' },
        { label: 'Years of Experience', value: '5+' },
        { label: 'Open Source Contributions', value: '200+' },
      ],
    },
    zh: {
      title: '关于 Tucsenberg',
      subtitle: '构建现代化前端开发的未来',
      mission: '我们的使命',
      vision: '我们的愿景',
      values: '我们的价值观',
      team: '我们的团队',
      contact: '联系我们',
      missionText:
        '为开发者提供最先进、高效且令人愉悦的现代化 Web 应用构建工具。我们相信通过技术赋能创造力。',
      visionText:
        '成为现代化前端开发的领先平台，为开发者体验和应用性能设立新的标准。',
      valuesList: [
        {
          title: '创新',
          description: '我们不断推动前端开发可能性的边界。',
          icon: Lightbulb,
        },
        {
          title: '质量',
          description: '我们在代码质量、性能和用户体验方面保持最高标准。',
          icon: Award,
        },
        {
          title: '社区',
          description: '我们相信开源和社区驱动开发的力量。',
          icon: Users,
        },
        {
          title: '性能',
          description: '我们在构建的每一个产品中都优化速度、效率和可扩展性。',
          icon: Zap,
        },
      ],
      teamMembers: [
        {
          name: 'Alex Chen',
          role: '首席前端架构师',
          description:
            '在 React、Next.js 和现代前端架构方面拥有 8+ 年经验的专家。',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
        {
          name: 'Sarah Johnson',
          role: 'UI/UX 设计师',
          description: '热衷于创建美观、无障碍且用户友好的界面。',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
        {
          name: 'Mike Rodriguez',
          role: 'DevOps 工程师',
          description: '专注于 CI/CD、性能优化和可扩展基础设施。',
          social: { github: '#', twitter: '#', linkedin: '#' },
        },
      ],
      stats: [
        { label: '交付项目', value: '100+' },
        { label: '满意客户', value: '50+' },
        { label: '经验年限', value: '5+' },
        { label: '开源贡献', value: '200+' },
      ],
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            {currentContent.title}
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {currentContent.stats.map((stat, index) => (
            <Card key={index} className="text-center">
              <CardContent className="pt-6">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-sm text-muted-foreground">
                  {stat.label}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mission & Vision */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                {currentContent.mission}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {currentContent.missionText}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-purple-600" />
                {currentContent.vision}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                {currentContent.visionText}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Values */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">
            {currentContent.values}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {currentContent.valuesList.map((value, index) => {
              const Icon = value.icon;
              return (
                <Card
                  key={index}
                  className="text-center hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-lg">{value.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {value.description}
                    </p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>

        {/* Team */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">
            {currentContent.team}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {currentContent.teamMembers.map((member, index) => (
              <Card
                key={index}
                className="text-center hover:shadow-lg transition-shadow"
              >
                <CardHeader>
                  <div className="mx-auto w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-4">
                    <Users className="h-10 w-10 text-white" />
                  </div>
                  <CardTitle className="text-xl">{member.name}</CardTitle>
                  <CardDescription className="text-blue-600 font-medium">
                    {member.role}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    {member.description}
                  </p>
                  <div className="flex justify-center gap-3">
                    <div className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground h-8 px-3">
                      <Github className="h-4 w-4" />
                    </div>
                    <div className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground h-8 px-3">
                      <Twitter className="h-4 w-4" />
                    </div>
                    <div className="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground h-8 px-3">
                      <Linkedin className="h-4 w-4" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Contact */}
        <div className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl flex items-center justify-center gap-2">
                <Heart className="h-6 w-6 text-red-500" />
                {currentContent.contact}
              </CardTitle>
              <CardDescription>
                {locale === 'en'
                  ? "Ready to start your next project? We'd love to hear from you!"
                  : '准备开始您的下一个项目？我们很乐意听到您的声音！'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ContactButton
                email="<EMAIL>"
                subject={locale === 'en' ? 'General Inquiry' : '一般咨询'}
                className="bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-10 px-6"
              >
                {locale === 'en' ? 'Contact Us' : '联系我们'}
              </ContactButton>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
