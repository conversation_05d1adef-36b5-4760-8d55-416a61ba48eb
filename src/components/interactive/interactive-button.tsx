'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';

/**
 * 客户端交互按钮组件
 *
 * 这个组件完全封装了交互逻辑，不接受任何函数类型的 props，
 * 符合 React 19 严格的服务器/客户端组件边界规则。
 *
 * 使用场景：
 * - 服务器组件中需要交互按钮
 * - 需要保持页面的静态生成优势
 * - 避免整个页面客户端化
 *
 * @example
 * // 导航按钮
 * <InteractiveButton actionType="navigation" href="/products">
 *   查看产品
 * </InteractiveButton>
 *
 * // 联系按钮
 * <InteractiveButton actionType="contact" email="<EMAIL>">
 *   发送邮件
 * </InteractiveButton>
 *
 * // 外部链接
 * <InteractiveButton actionType="external" href="https://example.com" target="_blank">
 *   访问网站
 * </InteractiveButton>
 */

// 安全的 Button props，排除所有函数类型
type SafeButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  | 'onClick'
  | 'onMouseDown'
  | 'onMouseUp'
  | 'onKeyDown'
  | 'onKeyUp'
  | 'onFocus'
  | 'onBlur'
  | 'onSubmit'
>;

interface InteractiveButtonProps extends SafeButtonProps {
  /**
   * 交互行为类型
   * - 'navigation': 内部页面导航
   * - 'contact': 联系相关操作（邮件、电话）
   * - 'download': 文件下载
   * - 'external': 外部链接跳转
   */
  actionType: 'navigation' | 'contact' | 'download' | 'external';

  /**
   * 导航目标 URL（用于 navigation 和 external 类型）
   */
  href?: string;

  /**
   * 邮箱地址（用于 contact 类型）
   */
  email?: string;

  /**
   * 电话号码（用于 contact 类型）
   */
  phone?: string;

  /**
   * 下载文件 URL（用于 download 类型）
   */
  downloadUrl?: string;

  /**
   * 下载文件名（用于 download 类型）
   */
  downloadFilename?: string;

  /**
   * 链接目标（用于 external 类型）
   */
  target?: '_blank' | '_self' | '_parent' | '_top';
}

export function InteractiveButton({
  actionType,
  href,
  email,
  phone,
  downloadUrl,
  downloadFilename,
  target = '_self',
  children,
  ...props
}: InteractiveButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      switch (actionType) {
        case 'navigation':
          if (href) {
            // 使用 Next.js 路由进行内部导航
            window.location.href = href;
          }
          break;

        case 'contact':
          if (email) {
            // 打开邮件客户端
            window.location.href = `mailto:${email}`;
          } else if (phone) {
            // 拨打电话
            window.location.href = `tel:${phone}`;
          }
          break;

        case 'download':
          if (downloadUrl) {
            // 创建下载链接
            const link = document.createElement('a');
            link.href = downloadUrl;
            if (downloadFilename) {
              link.download = downloadFilename;
            }
            link.target = '_blank';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
          break;

        case 'external':
          if (href) {
            // 打开外部链接
            window.open(href, target);
          }
          break;

        default:
          console.warn(`Unknown actionType: ${actionType}`);
      }
    },
    [actionType, href, email, phone, downloadUrl, downloadFilename, target]
  );

  return (
    <Button onClick={handleClick} {...props}>
      {children}
    </Button>
  );
}

/**
 * 便捷的导航按钮组件
 * 专门用于内部页面导航
 */
export function NavigationButton({
  href,
  children,
  ...props
}: Omit<InteractiveButtonProps, 'actionType'> & { href: string }) {
  return (
    <InteractiveButton actionType="navigation" href={href} {...props}>
      {children}
    </InteractiveButton>
  );
}

/**
 * 便捷的联系按钮组件
 * 专门用于联系相关操作
 */
export function ContactButton({
  email,
  phone,
  children,
  ...props
}: Omit<InteractiveButtonProps, 'actionType'> & {
  email?: string;
  phone?: string;
}) {
  return (
    <InteractiveButton
      actionType="contact"
      email={email}
      phone={phone}
      {...props}
    >
      {children}
    </InteractiveButton>
  );
}

/**
 * 便捷的外部链接按钮组件
 * 专门用于外部链接跳转
 */
export function ExternalLinkButton({
  href,
  target = '_blank',
  children,
  ...props
}: Omit<InteractiveButtonProps, 'actionType'> & {
  href: string;
  target?: string;
}) {
  return (
    <InteractiveButton
      actionType="external"
      href={href}
      target={target as '_blank' | '_self' | '_parent' | '_top'}
      {...props}
    >
      {children}
    </InteractiveButton>
  );
}
