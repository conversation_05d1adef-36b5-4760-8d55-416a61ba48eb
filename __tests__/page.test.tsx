import { describe, it, expect } from 'vitest';
import {
  renderAsyncComponent,
  screen,
  waitForAsyncComponent,
} from '@/lib/testing/utils';
import { createHomeTestWrapper } from '@/lib/testing/async-component-wrapper';

describe('Home Page', () => {
  const HomeTestWrapper = createHomeTestWrapper();

  it('renders the main heading in English', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    const heading = screen.getByRole('heading', {
      name: /tucsenberg web 1.0/i,
    });
    expect(heading).toBeInTheDocument();
  });

  it('renders the main heading in Chinese', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'zh' })} />
    );
    await waitForAsyncComponent();

    const heading = screen.getByRole('heading', {
      name: /tucsenberg web 1.0/i,
    });
    expect(heading).toBeInTheDocument();
  });

  it('displays feature cards in English', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getAllByText('Next.js 15 + React 19')).toHaveLength(2);
    expect(screen.getAllByText('shadcn/ui + Tailwind CSS')).toHaveLength(2);
    expect(screen.getAllByText('Zustand + React Query')).toHaveLength(2);
  });

  it('displays feature cards in Chinese', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'zh' })} />
    );
    await waitForAsyncComponent();

    expect(screen.getAllByText('Next.js 15 + React 19')).toHaveLength(2);
    expect(screen.getAllByText('shadcn/ui + Tailwind CSS')).toHaveLength(2);
    expect(screen.getAllByText('Zustand + React Query')).toHaveLength(2);
  });

  it('shows configuration completion status in English', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    const statusTitle = screen.getByText(
      'Tech Stack Configuration Complete ✅'
    );
    expect(statusTitle).toBeInTheDocument();
  });

  it('shows configuration completion status in Chinese', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'zh' })} />
    );
    await waitForAsyncComponent();

    const statusTitle = screen.getByText(
      'Tech Stack Configuration Complete ✅'
    );
    expect(statusTitle).toBeInTheDocument();
  });

  it('renders action buttons in English', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'en' })} />
    );
    await waitForAsyncComponent();

    const startButton = screen.getByRole('button', { name: /get started/i });
    const themeButton = screen.getByRole('button', { name: /toggle theme/i });

    expect(startButton).toBeInTheDocument();
    expect(themeButton).toBeInTheDocument();
  });

  it('renders action buttons in Chinese', async () => {
    await renderAsyncComponent(
      <HomeTestWrapper params={Promise.resolve({ locale: 'zh' })} />
    );
    await waitForAsyncComponent();

    const startButton = screen.getByRole('button', { name: /get started/i });
    const themeButton = screen.getByRole('button', { name: /toggle theme/i });

    expect(startButton).toBeInTheDocument();
    expect(themeButton).toBeInTheDocument();
  });
});
