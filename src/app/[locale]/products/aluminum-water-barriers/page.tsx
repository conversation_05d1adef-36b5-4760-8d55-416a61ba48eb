import { type Locale } from '@/lib/i18n/config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  LocalizedNavigationButton,
  PhoneButton,
  EmailButton,
} from '@/components/interactive';
import {
  Settings,
  Award,
  CheckCircle,
  ArrowLeft,
  MapPin,
  Star,
  Package,
} from 'lucide-react';

interface ProductPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function AluminumWaterBarriersPage({
  params,
}: ProductPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'Aluminum Custom Water Barriers',
      subtitle:
        'Premium custom-engineered aluminum flood barriers for critical applications',
      backToProducts: 'Back to Products',
      contactUs: 'Contact Us',
      getQuote: 'Get Quote',
      overview: 'Product Overview',
      features: 'Key Features',
      specifications: 'Technical Specifications',
      applications: 'Applications',
      advantages: 'Product Advantages',
      description:
        'Our aluminum custom water barriers represent the pinnacle of flood protection engineering. Precision-manufactured from marine-grade aluminum alloy, these barriers offer unmatched strength, corrosion resistance, and aesthetic appeal for high-value installations requiring both performance and visual integration.',
      featuresList: [
        'Marine-grade aluminum construction',
        'Custom engineering for each project',
        'Corrosion-resistant coating',
        'Precision manufacturing tolerances',
        'Aesthetic integration capability',
        'Permanent or removable options',
      ],
      specs: {
        material: 'Material',
        materialValue: 'Marine-grade 6061-T6 aluminum alloy',
        thickness: 'Wall Thickness',
        thicknessValue: '6-12mm (customizable)',
        height: 'Height Range',
        heightValue: '0.5m - 3.0m (custom)',
        coating: 'Surface Treatment',
        coatingValue: 'Anodized + Powder coating',
        tolerance: 'Manufacturing Tolerance',
        toleranceValue: '±0.5mm precision',
        warranty: 'Warranty Period',
        warrantyValue: '25 years structural',
      },
      applicationsList: [
        'High-end residential properties',
        'Commercial building protection',
        'Historic building preservation',
        'Luxury development projects',
        'Government facility protection',
        'Critical infrastructure safeguarding',
      ],
      advantagesList: [
        {
          title: 'Custom Engineering',
          description:
            'Each barrier system is engineered specifically for your site requirements and aesthetic preferences.',
        },
        {
          title: 'Premium Materials',
          description:
            'Marine-grade aluminum ensures maximum durability and corrosion resistance in any environment.',
        },
        {
          title: 'Aesthetic Integration',
          description:
            'Beautiful finishes and custom designs that complement architectural elements.',
        },
        {
          title: 'Long-term Value',
          description:
            '25-year warranty with minimal maintenance requirements for exceptional ROI.',
        },
      ],
    },
    zh: {
      title: '铝合金定制挡水板',
      subtitle: '高端定制工程铝合金防洪板，适用于关键应用场景',
      backToProducts: '返回产品',
      contactUs: '联系我们',
      getQuote: '获取报价',
      overview: '产品概述',
      features: '主要特性',
      specifications: '技术规格',
      applications: '应用场景',
      advantages: '产品优势',
      description:
        '我们的铝合金定制挡水板代表了防洪保护工程的巅峰。采用船用级铝合金精密制造，这些防洪板为需要性能和视觉集成的高价值安装提供无与伦比的强度、耐腐蚀性和美观性。',
      featuresList: [
        '船用级铝合金结构',
        '每个项目的定制工程',
        '耐腐蚀涂层',
        '精密制造公差',
        '美观集成能力',
        '永久或可拆卸选项',
      ],
      specs: {
        material: '材料',
        materialValue: '船用级6061-T6铝合金',
        thickness: '壁厚',
        thicknessValue: '6-12毫米（可定制）',
        height: '高度范围',
        heightValue: '0.5米 - 3.0米（定制）',
        coating: '表面处理',
        coatingValue: '阳极氧化 + 粉末涂层',
        tolerance: '制造公差',
        toleranceValue: '±0.5毫米精度',
        warranty: '保修期',
        warrantyValue: '25年结构保修',
      },
      applicationsList: [
        '高端住宅物业',
        '商业建筑保护',
        '历史建筑保护',
        '豪华开发项目',
        '政府设施保护',
        '关键基础设施防护',
      ],
      advantagesList: [
        {
          title: '定制工程',
          description:
            '每个防洪系统都专门针对您的场地要求和美学偏好进行工程设计。',
        },
        {
          title: '优质材料',
          description: '船用级铝合金确保在任何环境中的最大耐用性和耐腐蚀性。',
        },
        {
          title: '美观集成',
          description: '美丽的饰面和定制设计，与建筑元素完美融合。',
        },
        {
          title: '长期价值',
          description: '25年保修，维护要求最低，投资回报率卓越。',
        },
      ],
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 to-orange-50 dark:from-slate-900 dark:to-amber-900">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <LocalizedNavigationButton
            href="/products"
            variant="ghost"
            size="sm"
            className="text-amber-600 hover:text-amber-800 p-0 h-auto"
            showIcon={false}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentContent.backToProducts}
          </LocalizedNavigationButton>
        </div>

        {/* Hero Section */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="p-3 bg-amber-100 dark:bg-amber-900 rounded-lg">
                <Award className="h-8 w-8 text-amber-600 dark:text-amber-400" />
              </div>
              <Badge
                variant="secondary"
                className="text-sm bg-amber-100 text-amber-800"
              >
                Premium
              </Badge>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent mb-6">
              {currentContent.title}
            </h1>

            <p className="text-xl text-slate-600 dark:text-slate-300 mb-8">
              {currentContent.subtitle}
            </p>

            <div className="flex gap-4">
              <PhoneButton
                phone="+****************"
                size="lg"
                className="bg-amber-600 hover:bg-amber-700"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-amber-100 to-orange-100 dark:from-amber-900 dark:to-orange-900 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <Settings className="h-32 w-32 text-amber-500 mx-auto mb-4" />
                <p className="text-slate-600 dark:text-slate-300">
                  {currentContent.title}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Overview */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {currentContent.overview}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
              {currentContent.description}
            </p>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5 text-amber-500" />
                {currentContent.features}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.featuresList.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-blue-500" />
                {currentContent.specifications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.material}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.materialValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.thickness}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.thicknessValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.height}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.heightValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.coating}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.coatingValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.tolerance}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.toleranceValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.warranty}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.warrantyValue}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                {currentContent.applications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.applicationsList.map((application, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Star className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{application}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Advantages */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              {currentContent.advantages}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {currentContent.advantagesList.map((advantage, index) => (
                <div
                  key={index}
                  className="p-4 bg-amber-50 dark:bg-amber-900/20 rounded-lg"
                >
                  <h4 className="font-semibold mb-2">{advantage.title}</h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    {advantage.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-amber-600 to-orange-600 text-white">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'en'
                ? 'Ready for Premium Protection?'
                : '准备享受高端保护了吗？'}
            </h3>
            <p className="text-amber-100 mb-6">
              {locale === 'en'
                ? 'Contact our design engineers for bespoke aluminum barrier solutions.'
                : '联系我们的设计工程师获取定制铝合金防洪解决方案。'}
            </p>
            <div className="flex gap-4 justify-center">
              <PhoneButton
                phone="+****************"
                size="lg"
                variant="secondary"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
                className="text-white border-white hover:bg-white hover:text-amber-600"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
