/* 统一多语言字体样式配置 */

/* 字体大小系统 - 基于 1.25 比例 */
:root {
  /* 基础字体大小 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */

  /* 字重系统 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 1.75;

  /* 字间距系统 */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;
}

/* 中文特殊处理 */
[lang="zh"], [lang="zh-CN"] {
  --leading-normal: 1.7;
  --leading-relaxed: 1.8;
  --tracking-normal: 0.05em;
}

/* 字体层级样式 */

/* 第一层：品牌标识层 */
.font-brand {
  font-family: var(--font-geist), system-ui, sans-serif;
  font-weight: var(--font-bold);
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-tight);
}

[lang="zh"] .font-brand {
  font-family: var(--font-chinese-sc);
  font-weight: var(--font-medium);
  letter-spacing: var(--tracking-wider);
  line-height: 1.2;
}

/* 第二层：内容标题层 */
.font-heading {
  font-family: var(--font-geist), system-ui, sans-serif;
  font-weight: var(--font-semibold);
  letter-spacing: var(--tracking-tight);
  line-height: var(--leading-tight);
}

[lang="zh"] .font-heading {
  font-family: var(--font-chinese-sc);
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-wide);
  line-height: 1.3;
}

/* 第三层：正文阅读层 */
.font-body {
  font-family: var(--font-geist), system-ui, sans-serif;
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-normal);
}

[lang="zh"] .font-body {
  font-family: var(--font-chinese-sc);
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-wider);
  line-height: var(--leading-relaxed);
}

/* 响应式字体大小 */

/* 移动端优化 */
@media (max-width: 768px) {
  :root {
    --text-base: 0.9rem;
    --text-lg: 1rem;
    --text-xl: 1.125rem;
    --text-2xl: 1.25rem;
    --text-3xl: 1.5rem;
    --text-4xl: 1.875rem;
    --text-5xl: 2.25rem;
    --text-6xl: 3rem;
  }
  
  [lang="zh"] {
    --text-base: 0.95rem; /* 中文保持较大字号 */
    --tracking-normal: 0.03em;
  }
  
  .font-heading {
    line-height: 1.2;
    letter-spacing: var(--tracking-tight);
  }
}

/* 平板端优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  :root {
    --text-base: 1.05rem;
  }
  
  .font-body {
    line-height: 1.65;
  }
}

/* 桌面端精致化 */
@media (min-width: 1025px) {
  :root {
    --text-base: 1.125rem;
  }
  
  .font-desktop {
    font-feature-settings: 
      "kern" 1,      /* 字距调整 */
      "liga" 1,      /* 连字 */
      "calt" 1,      /* 上下文替代 */
      "ss01" 1;      /* 样式集 */
  }
}

/* 实用工具类 */

/* 字体大小工具类 */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }
.text-6xl { font-size: var(--text-6xl); }

/* 字重工具类 */
.font-light { font-weight: var(--font-light); }
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }
.font-extrabold { font-weight: var(--font-extrabold); }

/* 行高工具类 */
.leading-tight { line-height: var(--leading-tight); }
.leading-normal { line-height: var(--leading-normal); }
.leading-relaxed { line-height: var(--leading-relaxed); }
.leading-loose { line-height: var(--leading-loose); }

/* 字间距工具类 */
.tracking-tighter { letter-spacing: var(--tracking-tighter); }
.tracking-tight { letter-spacing: var(--tracking-tight); }
.tracking-normal { letter-spacing: var(--tracking-normal); }
.tracking-wide { letter-spacing: var(--tracking-wide); }
.tracking-wider { letter-spacing: var(--tracking-wider); }
.tracking-widest { letter-spacing: var(--tracking-widest); }

/* 特殊场景样式 */

/* 代码字体 */
.font-mono {
  font-family: var(--font-geist-mono), 'SF Mono', 'Consolas', monospace;
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" 1;
}

/* 数字字体 */
.font-numeric {
  font-variant-numeric: tabular-nums;
  font-feature-settings: "tnum" 1, "kern" 1;
}

/* 引用文字 */
.font-quote {
  font-style: italic;
  font-weight: var(--font-medium);
}

[lang="zh"] .font-quote {
  font-style: normal; /* 中文不使用斜体 */
  font-weight: var(--font-normal);
}

/* 强调文字 */
.font-emphasis {
  font-weight: var(--font-semibold);
  letter-spacing: var(--tracking-wide);
}

/* 小标题 */
.font-caption {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: rgb(107 114 128); /* text-gray-500 */
  letter-spacing: var(--tracking-wide);
}

/* 按钮文字 */
.font-button {
  font-weight: var(--font-medium);
  letter-spacing: var(--tracking-wide);
}

/* 链接文字 */
.font-link {
  font-weight: var(--font-medium);
  text-decoration: underline;
  text-underline-offset: 2px;
}

/* 性能优化类 */
.font-optimized {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 字体加载状态 */
.font-loading {
  font-family: system-ui, sans-serif;
  visibility: hidden;
}

.font-loaded {
  visibility: visible;
  transition: opacity 0.2s ease-in-out;
}

/* 打印样式 */
@media print {
  body {
    font-family: 'Times New Roman', serif;
    font-size: 12pt;
    line-height: 1.4;
  }
  
  [lang="zh"] {
    font-family: 'SimSun', serif;
  }
}
