import { describe, it, expect } from 'vitest';
import fs from 'fs';
import path from 'path';

/**
 * 静态生成验证测试
 *
 * 这些测试确保：
 * 1. 所有页面都能静态生成
 * 2. 没有使用 force-dynamic 配置
 * 3. 组件边界正确配置
 */

describe('Static Generation Validation', () => {
  const srcDir = path.join(process.cwd(), 'src');
  const appDir = path.join(srcDir, 'app');

  /**
   * 递归获取所有 TypeScript/JavaScript 文件
   */
  function getAllFiles(
    dir: string,
    extensions: string[] = ['.ts', '.tsx', '.js', '.jsx']
  ): string[] {
    const files: string[] = [];

    if (!fs.existsSync(dir)) {
      return files;
    }

    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);

      if (
        stat.isDirectory() &&
        !item.startsWith('.') &&
        item !== 'node_modules' &&
        item !== '__tests__'
      ) {
        files.push(...getAllFiles(fullPath, extensions));
      } else if (
        extensions.some((ext) => item.endsWith(ext)) &&
        !item.includes('.test.') &&
        !item.includes('.spec.')
      ) {
        files.push(fullPath);
      }
    }

    return files;
  }

  /**
   * 检查文件内容是否包含指定模式
   */
  function fileContains(filePath: string, pattern: string | RegExp): boolean {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      if (typeof pattern === 'string') {
        return content.includes(pattern);
      }
      return pattern.test(content);
    } catch {
      return false;
    }
  }

  /**
   * 获取所有页面文件
   */
  function getPageFiles(): string[] {
    const pageFiles: string[] = [];

    // App Router 页面文件
    const appFiles = getAllFiles(appDir, ['.tsx', '.ts']);
    pageFiles.push(
      ...appFiles.filter(
        (file) =>
          file.includes('/page.') ||
          file.includes('/layout.') ||
          file.includes('/loading.') ||
          file.includes('/error.')
      )
    );

    return pageFiles;
  }

  it('should not have any force-dynamic configurations', () => {
    const allFiles = getAllFiles(srcDir);
    const filesWithForceDynamic: string[] = [];

    for (const file of allFiles) {
      if (
        fileContains(
          file,
          /export\s+const\s+dynamic\s*=\s*['"`]force-dynamic['"`]/
        )
      ) {
        filesWithForceDynamic.push(file);
      }
    }

    expect(filesWithForceDynamic).toEqual([]);
  });

  it('should not have any revalidate = 0 configurations', () => {
    const allFiles = getAllFiles(srcDir);
    const filesWithRevalidateZero: string[] = [];

    for (const file of allFiles) {
      if (fileContains(file, /export\s+const\s+revalidate\s*=\s*0/)) {
        filesWithRevalidateZero.push(file);
      }
    }

    expect(filesWithRevalidateZero).toEqual([]);
  });

  it('should not pass functions as props to client components', () => {
    const allFiles = getAllFiles(srcDir, ['.tsx']);
    const violatingFiles: Array<{ file: string; lines: string[] }> = [];

    for (const file of allFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const lines = content.split('\n');
        const problematicLines: string[] = [];

        lines.forEach((line, index) => {
          // 检查是否在 JSX 中传递函数 props
          if (
            line.includes('onClick=') ||
            line.includes('onSubmit=') ||
            line.includes('onChange=') ||
            line.includes('onKeyDown=')
          ) {
            // 排除在客户端组件内部的使用（有 'use client' 指令）
            const hasUseClient =
              content.includes("'use client'") ||
              content.includes('"use client"');

            if (!hasUseClient) {
              problematicLines.push(`Line ${index + 1}: ${line.trim()}`);
            }
          }
        });

        if (problematicLines.length > 0) {
          violatingFiles.push({ file, lines: problematicLines });
        }
      } catch {
        // 忽略读取错误
      }
    }

    if (violatingFiles.length > 0) {
      const errorMessage = violatingFiles
        .map(({ file, lines }) => `${file}:\n${lines.join('\n')}`)
        .join('\n\n');

      expect.fail(
        `Found function props passed to components in server components:\n\n${errorMessage}`
      );
    }
  });

  it('should use proper interactive components instead of raw buttons', () => {
    const pageFiles = getPageFiles();
    const violatingFiles: Array<{ file: string; lines: string[] }> = [];

    for (const file of pageFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const lines = content.split('\n');
        const problematicLines: string[] = [];

        // 检查是否有 'use client' 指令
        const hasUseClient =
          content.includes("'use client'") || content.includes('"use client"');

        if (!hasUseClient) {
          lines.forEach((line, index) => {
            // 检查是否直接使用 Button 组件而不是专门的交互组件
            if (
              line.includes('<Button') &&
              (line.includes('onClick') || line.includes('onSubmit'))
            ) {
              problematicLines.push(`Line ${index + 1}: ${line.trim()}`);
            }
          });
        }

        if (problematicLines.length > 0) {
          violatingFiles.push({ file, lines: problematicLines });
        }
      } catch {
        // 忽略读取错误
      }
    }

    if (violatingFiles.length > 0) {
      const errorMessage = violatingFiles
        .map(({ file, lines }) => `${file}:\n${lines.join('\n')}`)
        .join('\n\n');

      expect.fail(
        `Found raw Button components with event handlers in server components. Use interactive components instead:\n\n${errorMessage}`
      );
    }
  });

  it('should have proper client component boundaries', () => {
    const interactiveDir = path.join(srcDir, 'components', 'interactive');

    if (!fs.existsSync(interactiveDir)) {
      expect.fail('Interactive components directory not found');
    }

    const interactiveFiles = getAllFiles(interactiveDir, ['.tsx']);
    const filesWithoutUseClient: string[] = [];

    for (const file of interactiveFiles) {
      if (
        !fileContains(file, "'use client'") &&
        !fileContains(file, '"use client"')
      ) {
        filesWithoutUseClient.push(file);
      }
    }

    expect(filesWithoutUseClient).toEqual([]);
  });

  it('should not import interactive components in server components without proper boundaries', () => {
    const pageFiles = getPageFiles();

    for (const file of pageFiles) {
      try {
        const content = fs.readFileSync(file, 'utf-8');
        const hasUseClient =
          content.includes("'use client'") || content.includes('"use client"');

        if (!hasUseClient) {
          const lines = content.split('\n');

          lines.forEach((line) => {
            // 检查是否导入了交互组件
            if (
              line.includes('from') &&
              (line.includes('@/components/interactive') ||
                line.includes('./components/interactive'))
            ) {
              // 这是允许的，因为交互组件本身是客户端组件
              // 但我们需要确保它们被正确使用
            }
          });
        }
      } catch {
        // 忽略读取错误
      }
    }

    // 这个测试主要是确保我们有正确的组件边界
    // 实际的违规会在其他测试中捕获
    expect(true).toBe(true);
  });

  it('should have TypeScript compilation without errors', () => {
    // 这个测试依赖于 TypeScript 编译
    // 在实际的 CI/CD 中，这会通过 tsc --noEmit 来验证

    const tsconfigPath = path.join(process.cwd(), 'tsconfig.json');
    expect(fs.existsSync(tsconfigPath)).toBe(true);

    // 检查是否有基本的 TypeScript 配置
    const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf-8'));
    expect(tsconfig.compilerOptions).toBeDefined();
    expect(tsconfig.compilerOptions.strict).toBe(true);
  });

  it('should have proper ESLint configuration for component boundaries', () => {
    const eslintConfigPath = path.join(process.cwd(), 'eslint.config.mjs');

    if (fs.existsSync(eslintConfigPath)) {
      const content = fs.readFileSync(eslintConfigPath, 'utf-8');

      // 检查是否有基本的 Next.js 配置
      expect(content).toContain('next');
    }

    // ESLint 配置存在即可，具体规则在其他地方验证
    expect(true).toBe(true);
  });
});
