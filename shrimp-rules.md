# AI Agent 開發守則

**專為 AI Coding Agent 設計的項目規範文件**

## 項目概述

### 技術棧限制
- **必須使用** Next.js 15 + React 19 + TypeScript 5.8+
- **必須使用** Tailwind CSS 4.1+ + shadcn/ui 組件
- **必須使用** Zustand 5.0+ 進行狀態管理
- **必須使用** @tanstack/react-query 5.80+ 進行數據獲取
- **必須使用** react-hook-form + zod 進行表單處理
- **禁止使用** 其他狀態管理庫（Redux、Context API 等）
- **禁止使用** 其他 UI 框架（Material-UI、Ant Design 等）

### 項目架構強制要求
- **嚴格遵循** App Router 架構，禁止使用 Pages Router
- **必須維持** 現有目錄結構，禁止重新組織主要目錄
- **必須使用** TypeScript 嚴格模式，禁止 any 類型

## 目錄結構規範

### 強制目錄結構
```
src/
├── app/[locale]/          # 國際化路由 - 禁止修改結構
├── components/
│   ├── ui/               # shadcn/ui 基礎組件 - 只能添加，禁止修改現有
│   ├── interactive/      # 客戶端交互組件 - 必須使用 'use client'
│   ├── navigation/       # 導航組件
│   ├── layout/          # 布局組件
│   ├── mdx/             # MDX 組件
│   └── analytics/       # 分析組件
├── lib/                 # 功能模塊 - 按功能分類
├── hooks/               # 自定義 Hooks
├── stores/              # Zustand 狀態管理
├── types/               # TypeScript 類型定義
└── __tests__/           # 測試文件
```

### 文件命名規範
- **組件文件** 使用 PascalCase：`UserProfile.tsx`
- **Hook 文件** 使用 kebab-case：`use-user-data.ts`
- **工具函數** 使用 kebab-case：`format-date.ts`
- **類型文件** 使用 kebab-case：`user-types.ts`

## React 19 組件開發規範

### useEffect 反模式防護
- **嚴格禁止** 在以下情況使用 useEffect：
  - 計算衍生狀態
  - 重置狀態
  - 調整 props 變化
  - 分享邏輯
  - 發送 POST 請求
- **必須通過** `npm run lint:ai-patterns` 檢查
- **客戶端組件** 中 useEffect 限制較寬鬆，但仍需謹慎使用

### 組件邊界規則
- **交互組件** 必須放在 `src/components/interactive/` 目錄
- **交互組件** 必須添加 `'use client'` 指令
- **服務端組件** 禁止使用客戶端 API（useState、useEffect 等）
- **必須區分** 靜態組件和交互組件，禁止混合使用

### 組件結構要求
```typescript
// 正確的組件結構
'use client' // 僅交互組件需要

import { ... } from '...'

interface ComponentProps {
  // 類型定義
}

export function ComponentName({ ...props }: ComponentProps) {
  // 組件邏輯
  return (
    // JSX
  )
}
```

## 多語言處理規範

### 內容同步要求
- **修改 content/en/** 時必須同步更新 **content/zh/**
- **修改 README.md** 時必須檢查是否需要更新相關文檔
- **添加新頁面** 時必須創建對應的多語言版本
- **路由結構** 必須保持 `[locale]` 參數

### 國際化文件規範
- **內容文件** 使用 MDX 格式
- **翻譯鍵值** 使用 kebab-case
- **必須維護** 語言文件的結構一致性

## 測試要求

### 測試文件結構
```
__tests__/
├── components/          # 組件測試
├── integration/         # 集成測試
├── lib/                # 工具函數測試
└── pages/              # 頁面測試
```

### 測試規範
- **新增組件** 必須添加對應測試文件
- **測試文件** 命名格式：`ComponentName.test.tsx`
- **必須通過** `npm run test` 檢查
- **集成測試** 必須覆蓋關鍵用戶流程

## 代碼質量規範

### 必須通過的檢查
- `npm run typecheck` - TypeScript 類型檢查
- `npm run lint` - ESLint 代碼檢查
- `npm run lint:ai-patterns` - AI 反模式檢查
- `npm run test` - 單元測試
- `npm run format:check` - 代碼格式檢查

### 提交前要求
- **必須運行** `npm run check:all` 並通過所有檢查
- **自動格式化** 通過 lint-staged 配置執行
- **禁止提交** 未通過檢查的代碼

## 腳本使用規範

### 開發腳本
- `npm run dev` - 啟動開發服務器
- `npm run test:watch` - 監聽模式運行測試
- `npm run format:safe` - 安全格式化代碼

### 檢查腳本
- `npm run check:all` - 運行完整檢查套件
- `npm run check:build` - 構建前檢查
- `npm run security:check` - 安全檢查

### 格式化腳本
- **使用** `npm run format:safe` 進行代碼格式化
- **禁止** 直接使用 prettier 命令
- **必須** 在格式化前創建備份

## 依賴管理規範

### 包管理器要求
- **必須使用** npm 作為包管理器
- **禁止使用** yarn 或 pnpm
- **添加依賴** 前必須檢查是否與現有依賴衝突

### 依賴更新規則
- **核心依賴** 更新需要運行完整測試套件
- **開發依賴** 更新需要驗證構建流程
- **必須更新** package-lock.json

## 文件交互規範

### 關鍵文件連動修改
- **修改 package.json** → 運行 `npm install` 和 `npm run check:all`
- **修改 eslint.config.mjs** → 運行 `npm run lint` 驗證配置
- **修改 tailwind.config.ts** → 檢查樣式是否正常
- **修改 next.config.ts** → 運行 `npm run build` 驗證配置
- **添加環境變量** → 更新 `src/env.ts` 和相關文檔

### 文檔同步要求
- **修改功能** → 更新相關 docs/ 文件
- **添加組件** → 更新組件使用指南
- **修改配置** → 更新 README.md 相關部分

## AI 決策規範

### 優先級判斷
1. **安全性** > 功能性 > 性能 > 美觀性
2. **類型安全** > 代碼簡潔 > 運行效率
3. **測試覆蓋** > 開發速度 > 代碼量

### 模糊情況處理
- **不確定組件類型** → 優先選擇服務端組件
- **不確定測試範圍** → 優先添加單元測試
- **不確定依賴選擇** → 優先使用項目現有依賴
- **不確定目錄位置** → 參考現有相似文件的位置

## 嚴格禁止事項

### 架構禁止
- **禁止** 修改 Next.js App Router 結構
- **禁止** 使用 Pages Router
- **禁止** 繞過 TypeScript 類型檢查
- **禁止** 在服務端組件中使用客戶端 API

### 代碼禁止
- **禁止** 使用 `any` 類型
- **禁止** 忽略 ESLint 錯誤
- **禁止** 跳過測試編寫
- **禁止** 直接修改 node_modules

### 工作流程禁止
- **禁止** 提交未格式化的代碼
- **禁止** 跳過 pre-commit hooks
- **禁止** 忽略構建錯誤
- **禁止** 修改 lint-staged 配置

### 依賴禁止
- **禁止** 安裝未經驗證的第三方包
- **禁止** 使用過時或不安全的依賴
- **禁止** 混合使用不同的包管理器

## 應該做的事情範例

### ✅ 正確做法
- 添加新組件時創建對應測試文件
- 使用 `npm run format:safe` 格式化代碼
- 在交互組件中正確使用 `'use client'`
- 使用 Zustand 管理全局狀態
- 使用 React Query 處理服務端數據
- 遵循現有的目錄結構和命名規範

### ❌ 錯誤做法
- 在服務端組件中使用 useState
- 跳過測試編寫直接提交代碼
- 使用 any 類型繞過類型檢查
- 修改核心配置文件而不運行驗證
- 添加未經測試的第三方依賴
- 忽略 ESLint 和 TypeScript 錯誤

## 環境配置規範

### 環境變量管理
- **必須使用** `src/env.ts` 管理環境變量
- **添加新環境變量** 時必須更新 `env.ts` 中的 schema
- **必須提供** 開發環境的默認值
- **敏感信息** 必須使用環境變量，禁止硬編碼

### 必需環境變量
```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000  # 必需
```

### 可選服務配置
- **PostHog**: `NEXT_PUBLIC_POSTHOG_KEY`, `NEXT_PUBLIC_POSTHOG_HOST`
- **Google Analytics**: `NEXT_PUBLIC_GA_MEASUREMENT_ID`
- **Sentry**: `NEXT_PUBLIC_SENTRY_DSN`
- **Resend**: `RESEND_TOKEN`, `RESEND_FROM`
- **Redis**: `UPSTASH_REDIS_REST_URL`, `UPSTASH_REDIS_REST_TOKEN`

## 性能優化規範

### 圖片處理
- **必須使用** Next.js Image 組件
- **必須提供** alt 屬性
- **必須指定** width 和 height
- **優先使用** WebP 格式

### 代碼分割
- **動態導入** 大型組件和庫
- **使用** React.lazy 進行組件懶加載
- **避免** 在服務端組件中進行不必要的客戶端導入

### 緩存策略
- **靜態內容** 使用 Next.js 靜態生成
- **動態數據** 使用 React Query 緩存
- **全局狀態** 使用 Zustand 持久化

## 安全規範

### 輸入驗證
- **所有用戶輸入** 必須使用 zod 驗證
- **API 路由** 必須驗證請求參數
- **表單數據** 必須在客戶端和服務端雙重驗證

### 內容安全
- **禁止** 直接渲染用戶提供的 HTML
- **使用** DOMPurify 清理不安全內容
- **API 密鑰** 必須使用環境變量管理

## 錯誤處理規範

### 錯誤邊界
- **必須實現** React Error Boundary
- **錯誤組件** 放置在 `src/components/error/`
- **必須記錄** 錯誤到監控服務

### API 錯誤處理
- **使用** React Query 的錯誤處理機制
- **提供** 用戶友好的錯誤信息
- **實現** 重試機制

## 可訪問性規範

### ARIA 標準
- **必須提供** 適當的 ARIA 標籤
- **確保** 鍵盤導航支持
- **測試** 屏幕閱讀器兼容性

### 顏色對比
- **確保** 文字與背景對比度符合 WCAG 2.1 AA 標準
- **提供** 深色模式支持
- **避免** 僅依賴顏色傳達信息

## 監控和分析規範

### 性能監控
- **集成** Vercel Analytics（生產環境自動啟用）
- **可選集成** PostHog 或 Google Analytics
- **監控** Core Web Vitals 指標

### 錯誤追蹤
- **可選集成** Sentry 進行錯誤追蹤
- **記錄** 關鍵用戶操作
- **設置** 錯誤告警

## 部署規範

### 構建要求
- **必須通過** `npm run check:build` 檢查
- **必須通過** 所有測試
- **必須** 生成無錯誤的構建輸出

### 環境配置
- **開發環境** 使用 `.env.local`
- **生產環境** 使用平台環境變量
- **必須驗證** 所有必需環境變量

## 版本控制規範

### Git 工作流程
- **使用** 語義化提交信息
- **必須通過** pre-commit hooks
- **禁止** 提交 node_modules 或構建文件

### 分支策略
- **主分支** 保持穩定可部署狀態
- **功能分支** 從主分支創建
- **合併前** 必須通過所有檢查

---

**此規範文件專為 AI Agent 設計，必須嚴格遵循所有規則和限制。**
**更新日期：2024年12月**
