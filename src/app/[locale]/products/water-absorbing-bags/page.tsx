import { type Locale } from '@/lib/i18n/config';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  LocalizedNavigationButton,
  PhoneButton,
  EmailButton,
} from '@/components/interactive';
import {
  Droplets,
  Shield,
  Zap,
  CheckCircle,
  ArrowLeft,
  MapPin,
  Star,
  Package,
} from 'lucide-react';

interface ProductPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function WaterAbsorbingBagsPage({
  params,
}: ProductPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'Water Absorbing Bags',
      subtitle:
        'Professional flood protection with instant water absorption technology',
      backToProducts: 'Back to Products',
      contactUs: 'Contact Us',
      getQuote: 'Get Quote',
      overview: 'Product Overview',
      features: 'Key Features',
      specifications: 'Technical Specifications',
      applications: 'Applications',
      advantages: 'Product Advantages',
      description:
        'Our water absorbing bags provide rapid flood protection through advanced polymer technology. These lightweight, easy-to-deploy bags expand quickly when in contact with water, creating an effective barrier against flooding.',
      featuresList: [
        'Rapid water absorption within 3-5 minutes',
        'Expands up to 20 times original size',
        'Lightweight and easy to transport',
        'Environmentally friendly materials',
        'Long-term storage capability',
        'No sand or filling required',
      ],
      specs: {
        weight: 'Weight (Dry)',
        weightValue: '0.4kg per bag',
        absorption: 'Water Absorption',
        absorptionValue: 'Up to 20L per bag',
        expansion: 'Expansion Time',
        expansionValue: '3-5 minutes',
        dimensions: 'Expanded Dimensions',
        dimensionsValue: '60cm × 40cm × 15cm',
        material: 'Material',
        materialValue: 'Super absorbent polymer + Non-woven fabric',
        shelfLife: 'Shelf Life',
        shelfLifeValue: '5 years',
      },
      applicationsList: [
        'Emergency flood protection',
        'Construction site water management',
        'Basement and garage protection',
        'Industrial facility safeguarding',
        'Temporary water diversion',
        'Storm water management',
      ],
      advantagesList: [
        {
          title: 'Quick Deployment',
          description:
            'No tools or special training required. Simply place and add water.',
        },
        {
          title: 'Cost Effective',
          description:
            'Significantly cheaper than traditional sandbags with better performance.',
        },
        {
          title: 'Space Efficient',
          description:
            'Compact storage saves 95% space compared to pre-filled sandbags.',
        },
        {
          title: 'Reusable',
          description:
            'Can be dried and reused multiple times with proper care.',
        },
      ],
    },
    zh: {
      title: '吸水膨胀袋',
      subtitle: '专业防洪保护，采用瞬间吸水膨胀技术',
      backToProducts: '返回产品',
      contactUs: '联系我们',
      getQuote: '获取报价',
      overview: '产品概述',
      features: '主要特性',
      specifications: '技术规格',
      applications: '应用场景',
      advantages: '产品优势',
      description:
        '我们的吸水膨胀袋通过先进的聚合物技术提供快速防洪保护。这些轻便、易于部署的袋子在接触水时快速膨胀，形成有效的防洪屏障。',
      featuresList: [
        '3-5分钟内快速吸水',
        '膨胀至原体积的20倍',
        '轻便易于运输',
        '环保材料制造',
        '长期储存能力',
        '无需沙土或填充物',
      ],
      specs: {
        weight: '重量（干燥状态）',
        weightValue: '每袋0.4公斤',
        absorption: '吸水量',
        absorptionValue: '每袋最多20升',
        expansion: '膨胀时间',
        expansionValue: '3-5分钟',
        dimensions: '膨胀后尺寸',
        dimensionsValue: '60厘米 × 40厘米 × 15厘米',
        material: '材料',
        materialValue: '超强吸水聚合物 + 无纺布',
        shelfLife: '保质期',
        shelfLifeValue: '5年',
      },
      applicationsList: [
        '紧急防洪保护',
        '建筑工地水管理',
        '地下室和车库保护',
        '工业设施防护',
        '临时导水',
        '雨水管理',
      ],
      advantagesList: [
        {
          title: '快速部署',
          description: '无需工具或特殊培训，只需放置并加水即可。',
        },
        {
          title: '成本效益',
          description: '比传统沙袋显著便宜，性能更佳。',
        },
        {
          title: '节省空间',
          description: '紧凑存储，比预填充沙袋节省95%空间。',
        },
        {
          title: '可重复使用',
          description: '适当保养下可干燥并多次重复使用。',
        },
      ],
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <div className="mb-8">
          <LocalizedNavigationButton
            href="/products"
            variant="ghost"
            size="sm"
            className="text-blue-600 hover:text-blue-800 p-0 h-auto"
            showIcon={false}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {currentContent.backToProducts}
          </LocalizedNavigationButton>
        </div>

        {/* Hero Section */}
        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          <div>
            <div className="flex items-center gap-3 mb-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Droplets className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <Badge variant="secondary" className="text-sm">
                {currentContent.features}
              </Badge>
            </div>

            <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-blue-600 to-cyan-600 bg-clip-text text-transparent mb-6">
              {currentContent.title}
            </h1>

            <p className="text-xl text-slate-600 dark:text-slate-300 mb-8">
              {currentContent.subtitle}
            </p>

            <div className="flex gap-4">
              <PhoneButton
                phone="+****************"
                size="lg"
                className="bg-blue-600 hover:bg-blue-700"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </div>

          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-blue-100 to-cyan-100 dark:from-blue-900 dark:to-cyan-900 rounded-2xl flex items-center justify-center">
              <div className="text-center">
                <Droplets className="h-32 w-32 text-blue-500 mx-auto mb-4" />
                <p className="text-slate-600 dark:text-slate-300">
                  {currentContent.title}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Product Overview */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {currentContent.overview}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-lg text-slate-600 dark:text-slate-300 leading-relaxed">
              {currentContent.description}
            </p>
          </CardContent>
        </Card>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                {currentContent.features}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.featuresList.map((feature, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{feature}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-500" />
                {currentContent.specifications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.weight}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.weightValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.absorption}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.absorptionValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.expansion}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.expansionValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.dimensions}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.dimensionsValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.material}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.materialValue}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="font-medium">
                    {currentContent.specs.shelfLife}:
                  </span>
                  <span className="text-slate-600 dark:text-slate-300">
                    {currentContent.specs.shelfLifeValue}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-green-500" />
                {currentContent.applications}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                {currentContent.applicationsList.map((application, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <Star className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{application}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Advantages */}
        <Card className="mb-12">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              {currentContent.advantages}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              {currentContent.advantagesList.map((advantage, index) => (
                <div
                  key={index}
                  className="p-4 bg-slate-50 dark:bg-slate-800 rounded-lg"
                >
                  <h4 className="font-semibold mb-2">{advantage.title}</h4>
                  <p className="text-sm text-slate-600 dark:text-slate-300">
                    {advantage.description}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* CTA Section */}
        <Card className="bg-gradient-to-r from-blue-600 to-cyan-600 text-white">
          <CardContent className="p-8 text-center">
            <h3 className="text-2xl font-bold mb-4">
              {locale === 'en'
                ? 'Ready to Protect Your Property?'
                : '准备保护您的财产了吗？'}
            </h3>
            <p className="text-blue-100 mb-6">
              {locale === 'en'
                ? 'Contact our experts for customized flood protection solutions.'
                : '联系我们的专家获取定制化防洪保护解决方案。'}
            </p>
            <div className="flex gap-4 justify-center">
              <PhoneButton
                phone="+****************"
                size="lg"
                variant="secondary"
              >
                {currentContent.contactUs}
              </PhoneButton>
              <EmailButton
                email="<EMAIL>"
                subject={`${locale === 'en' ? 'Quote Request for' : '报价咨询'} ${currentContent.title}`}
                size="lg"
                variant="outline"
                className="text-white border-white hover:bg-white hover:text-blue-600"
              >
                {currentContent.getQuote}
              </EmailButton>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
