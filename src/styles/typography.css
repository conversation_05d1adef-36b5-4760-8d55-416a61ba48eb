/* 美观导向的字体样式系统 */

:root {
  /* 字体大小比例系统 - 基于 1.25 (Major Third) */
  --text-xs: 0.64rem;   /* 10.24px */
  --text-sm: 0.8rem;    /* 12.8px */
  --text-base: 1rem;    /* 16px */
  --text-lg: 1.25rem;   /* 20px */
  --text-xl: 1.563rem;  /* 25px */
  --text-2xl: 1.953rem; /* 31.25px */
  --text-3xl: 2.441rem; /* 39.06px */
  --text-4xl: 3.052rem; /* 48.83px */
  --text-5xl: 3.815rem; /* 61.04px */
  --text-6xl: 4.768rem; /* 76.29px */

  /* 字重层级 */
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  /* 行高系统 */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* 字间距系统 */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;
  --tracking-widest: 0.1em;

  /* 文本颜色系统 */
  --text-primary: #1a1a1a;
  --text-secondary: #4a4a4a;
  --text-tertiary: #8a8a8a;
  --text-quaternary: #c4c4c4;
  
  /* 品牌色彩文本 */
  --text-brand: #2563eb;
  --text-accent: #7c3aed;
  --text-success: #059669;
  --text-warning: #d97706;
  --text-error: #dc2626;
}

/* 深色模式文本颜色 */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f5f5f5;
    --text-secondary: #d4d4d4;
    --text-tertiary: #a3a3a3;
    --text-quaternary: #525252;
  }
}

/* 基础字体设置 */
body {
  font-family: var(--font-inter), 'SF Pro Text', system-ui, sans-serif;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  font-feature-settings: 
    "kern" 1,      /* 字距调整 */
    "liga" 1,      /* 连字 */
    "calt" 1;      /* 上下文替代 */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 语言特定字体设置 */
[lang="zh"], [lang="zh-CN"] {
  font-family: var(--font-noto-sc), 'PingFang SC', 'Source Han Sans SC', sans-serif;
  letter-spacing: var(--tracking-wide);
  line-height: var(--leading-relaxed);
  --text-primary: #2a2a2a; /* 中文稍深，增强对比 */
}

[lang="ja"] {
  font-family: var(--font-noto-jp), 'Hiragino Kaku Gothic ProN', 'Yu Gothic', sans-serif;
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-relaxed);
}

[lang="ko"] {
  font-family: var(--font-noto-kr), 'Apple SD Gothic Neo', 'Malgun Gothic', sans-serif;
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-relaxed);
}

/* 标题层级系统 */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-geist), 'SF Pro Display', system-ui, sans-serif;
  font-weight: var(--font-semibold);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  color: var(--text-primary);
  margin: 0;
}

h1 {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  letter-spacing: var(--tracking-tighter);
  line-height: 1.1;
}

h2 {
  font-size: var(--text-3xl);
  font-weight: var(--font-semibold);
}

h3 {
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
}

h4 {
  font-size: var(--text-xl);
  font-weight: var(--font-medium);
}

h5 {
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
}

h6 {
  font-size: var(--text-base);
  font-weight: var(--font-medium);
}

/* 中文标题特殊处理 */
[lang="zh"] h1, [lang="zh"] h2, [lang="zh"] h3,
[lang="zh"] h4, [lang="zh"] h5, [lang="zh"] h6 {
  font-family: var(--font-noto-sc), 'Source Han Sans SC', 'PingFang SC', sans-serif;
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-snug);
}

/* 段落和文本 */
p {
  margin: 0 0 1rem 0;
  line-height: var(--leading-normal);
}

/* 强调文本 */
strong, b {
  font-weight: var(--font-semibold);
}

em, i {
  font-style: italic;
}

/* 链接样式 */
a {
  color: var(--text-brand);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--text-accent);
  text-decoration: underline;
}

/* 引用样式 */
blockquote {
  font-style: italic;
  font-weight: var(--font-medium);
  border-left: 4px solid var(--text-accent);
  padding-left: 1.5rem;
  margin: 2rem 0;
  color: var(--text-secondary);
  font-size: var(--text-lg);
}

/* 代码样式 */
code {
  font-family: 'JetBrains Mono', 'SF Mono', 'Consolas', monospace;
  font-size: 0.875em;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: var(--font-medium);
}

pre code {
  background: none;
  padding: 0;
}

/* 列表样式 */
ul, ol {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

li {
  margin-bottom: 0.5rem;
  line-height: var(--leading-normal);
}

/* 响应式字体调整 */
@media (max-width: 768px) {
  :root {
    --text-base: 0.9rem;
    --text-4xl: 2.5rem;
    --text-3xl: 2rem;
    --text-2xl: 1.75rem;
  }
  
  /* 中文移动端特殊处理 */
  [lang="zh"] {
    --text-base: 0.95rem;
    letter-spacing: var(--tracking-normal);
  }
  
  h1, h2, h3 {
    line-height: var(--leading-tight);
    letter-spacing: var(--tracking-tighter);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  :root {
    --text-base: 1.05rem;
  }
  
  body {
    line-height: var(--leading-relaxed);
  }
}

@media (min-width: 1025px) {
  :root {
    --text-base: 1.125rem;
  }
  
  /* 桌面端精致排版 */
  .desktop-typography {
    font-feature-settings: 
      "kern" 1,      /* 字距调整 */
      "liga" 1,      /* 连字 */
      "calt" 1,      /* 上下文替代 */
      "ss01" 1;      /* 样式集 */
  }
}

/* 特殊美学类 */
.hero-title {
  font-family: var(--font-geist), system-ui, sans-serif;
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: var(--font-bold);
  letter-spacing: var(--tracking-tighter);
  line-height: 1.1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

[lang="zh"] .hero-title {
  font-family: var(--font-noto-sc), 'Source Han Sans SC', sans-serif;
  letter-spacing: var(--tracking-wide);
  line-height: 1.2;
}

.elegant-text {
  font-family: 'Crimson Pro', 'Georgia', 'Times New Roman', serif;
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-relaxed);
}

.modern-text {
  font-family: var(--font-geist), system-ui, sans-serif;
  font-weight: var(--font-medium);
  letter-spacing: var(--tracking-tight);
  text-rendering: optimizeLegibility;
}

.friendly-text {
  font-family: var(--font-inter), system-ui, sans-serif;
  font-weight: var(--font-normal);
  letter-spacing: var(--tracking-normal);
  line-height: var(--leading-relaxed);
}
