import { type Locale } from '@/lib/i18n/config';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ContactFormSubmitButton,
  EmailButton,
  PhoneButton,
} from '@/components/interactive';
import { Mail, Phone, MapPin, Clock, Send } from 'lucide-react';

interface ContactPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function ContactPage({ params }: ContactPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'Contact Us',
      subtitle: 'Get in touch with our flood protection experts',
      description:
        "We're here to help you find the right flood protection solution for your needs. Contact us today for a consultation.",
      contactInfo: 'Contact Information',
      businessHours: 'Business Hours',
      getInTouch: 'Get in Touch',
      sendMessage: 'Send Message',
      info: {
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Protection Street, Safety City, SC 12345',
        hours: 'Monday - Friday: 9:00 AM - 6:00 PM',
      },
      form: {
        name: 'Full Name',
        email: 'Email Address',
        subject: 'Subject',
        message: 'Message',
        namePlaceholder: 'Enter your full name',
        emailPlaceholder: 'Enter your email address',
        subjectPlaceholder: 'What can we help you with?',
        messagePlaceholder: 'Tell us about your flood protection needs...',
      },
    },
    zh: {
      title: '联系我们',
      subtitle: '与我们的防洪保护专家取得联系',
      description:
        '我们在这里帮助您找到适合您需求的防洪保护解决方案。立即联系我们进行咨询。',
      contactInfo: '联系信息',
      businessHours: '营业时间',
      getInTouch: '取得联系',
      sendMessage: '发送消息',
      info: {
        email: '<EMAIL>',
        phone: '+****************',
        address: '安全城保护街123号，SC 12345',
        hours: '周一至周五：上午9:00 - 下午6:00',
      },
      form: {
        name: '姓名',
        email: '邮箱地址',
        subject: '主题',
        message: '消息',
        namePlaceholder: '请输入您的姓名',
        emailPlaceholder: '请输入您的邮箱地址',
        subjectPlaceholder: '我们可以为您提供什么帮助？',
        messagePlaceholder: '请告诉我们您的防洪保护需求...',
      },
    },
  };

  const currentContent = content[locale];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            {currentContent.title}
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
          <p className="text-lg text-slate-500 dark:text-slate-400 max-w-2xl mx-auto">
            {currentContent.description}
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Contact Information */}
          <div className="space-y-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5 text-blue-500" />
                  {currentContent.contactInfo}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-slate-500" />
                  <div className="flex-1">
                    <p className="font-medium mb-2">Email</p>
                    <EmailButton
                      email={currentContent.info.email}
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                    >
                      {currentContent.info.email}
                    </EmailButton>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Phone className="h-5 w-5 text-slate-500" />
                  <div className="flex-1">
                    <p className="font-medium mb-2">Phone</p>
                    <PhoneButton
                      phone={currentContent.info.phone}
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                    >
                      {currentContent.info.phone}
                    </PhoneButton>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <MapPin className="h-5 w-5 text-slate-500 mt-0.5" />
                  <div>
                    <p className="font-medium">Address</p>
                    <p className="text-slate-600 dark:text-slate-300">
                      {currentContent.info.address}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-green-500" />
                  {currentContent.businessHours}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-slate-600 dark:text-slate-300">
                  {currentContent.info.hours}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Send className="h-5 w-5 text-purple-500" />
                {currentContent.getInTouch}
              </CardTitle>
              <CardDescription>
                {locale === 'en'
                  ? "Fill out the form below and we'll get back to you as soon as possible."
                  : '填写下面的表格，我们会尽快回复您。'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {currentContent.form.name}
                    </label>
                    <input
                      type="text"
                      name="name"
                      placeholder={currentContent.form.namePlaceholder}
                      className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      {currentContent.form.email}
                    </label>
                    <input
                      type="email"
                      name="email"
                      placeholder={currentContent.form.emailPlaceholder}
                      className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {currentContent.form.subject}
                  </label>
                  <input
                    type="text"
                    name="subject"
                    placeholder={currentContent.form.subjectPlaceholder}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    {currentContent.form.message}
                  </label>
                  <textarea
                    rows={6}
                    name="message"
                    placeholder={currentContent.form.messagePlaceholder}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  />
                </div>

                <ContactFormSubmitButton
                  size="lg"
                  className="w-full bg-blue-600 hover:bg-blue-700"
                  email={currentContent.info.email}
                  subject={
                    locale === 'en' ? 'Contact Form Submission' : '联系表单提交'
                  }
                  loadingText={locale === 'en' ? 'Sending...' : '发送中...'}
                >
                  {currentContent.sendMessage}
                </ContactFormSubmitButton>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
