'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Mail, Phone, MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * 专门的联系交互组件
 *
 * 这些组件专门用于处理联系相关的交互，包括邮件、电话等。
 * 完全封装交互逻辑，符合 React 19 严格的组件边界规则。
 *
 * @example
 * // 邮件联系按钮
 * <EmailButton email="<EMAIL>" variant="default" size="lg">
 *   发送邮件
 * </EmailButton>
 *
 * // 电话联系按钮
 * <PhoneButton phone="******-123-4567" variant="outline">
 *   立即致电
 * </PhoneButton>
 *
 * // 通用联系按钮
 * <ContactButton
 *   email="<EMAIL>"
 *   phone="******-123-4567"
 *   preferredMethod="email"
 * >
 *   联系我们
 * </ContactButton>
 */

// 安全的 Button props，排除所有函数类型
type SafeButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  | 'onClick'
  | 'onMouseDown'
  | 'onMouseUp'
  | 'onKeyDown'
  | 'onKeyUp'
  | 'onFocus'
  | 'onBlur'
  | 'onSubmit'
>;

/**
 * 邮件联系按钮组件
 */
interface EmailButtonProps extends SafeButtonProps {
  /**
   * 邮箱地址
   */
  email: string;

  /**
   * 邮件主题（可选）
   */
  subject?: string;

  /**
   * 邮件正文（可选）
   */
  body?: string;

  /**
   * 是否显示邮件图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function EmailButton({
  email,
  subject,
  body,
  showIcon = true,
  iconPosition = 'left',
  children,
  className,
  ...props
}: EmailButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      // 构建 mailto URL
      let mailtoUrl = `mailto:${email}`;
      const params = new URLSearchParams();

      if (subject) {
        params.append('subject', subject);
      }

      if (body) {
        params.append('body', body);
      }

      if (params.toString()) {
        mailtoUrl += `?${params.toString()}`;
      }

      window.location.href = mailtoUrl;
    },
    [email, subject, body]
  );

  const icon = showIcon ? <Mail className="h-4 w-4" /> : null;

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={`Send email to ${email}`}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 电话联系按钮组件
 */
interface PhoneButtonProps extends SafeButtonProps {
  /**
   * 电话号码
   */
  phone: string;

  /**
   * 是否显示电话图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function PhoneButton({
  phone,
  showIcon = true,
  iconPosition = 'left',
  children,
  className,
  ...props
}: PhoneButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      // 清理电话号码，移除非数字字符（保留 + 号）
      const cleanPhone = phone.replace(/[^\d+\-\s()]/g, '');
      window.location.href = `tel:${cleanPhone}`;
    },
    [phone]
  );

  const icon = showIcon ? <Phone className="h-4 w-4" /> : null;

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={`Call ${phone}`}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 通用联系按钮组件
 */
interface ContactButtonProps extends SafeButtonProps {
  /**
   * 邮箱地址（可选）
   */
  email?: string;

  /**
   * 电话号码（可选）
   */
  phone?: string;

  /**
   * 首选联系方式
   */
  preferredMethod?: 'email' | 'phone';

  /**
   * 邮件主题（当使用邮件时）
   */
  subject?: string;

  /**
   * 邮件正文（当使用邮件时）
   */
  body?: string;

  /**
   * 是否显示图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function ContactButton({
  email,
  phone,
  preferredMethod = 'email',
  subject,
  body,
  showIcon = true,
  iconPosition = 'left',
  children,
  className,
  ...props
}: ContactButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      // 根据首选方式和可用选项决定联系方式
      if (preferredMethod === 'email' && email) {
        // 使用邮件
        let mailtoUrl = `mailto:${email}`;
        const params = new URLSearchParams();

        if (subject) {
          params.append('subject', subject);
        }

        if (body) {
          params.append('body', body);
        }

        if (params.toString()) {
          mailtoUrl += `?${params.toString()}`;
        }

        window.location.href = mailtoUrl;
      } else if (preferredMethod === 'phone' && phone) {
        // 使用电话
        const cleanPhone = phone.replace(/[^\d+\-\s()]/g, '');
        window.location.href = `tel:${cleanPhone}`;
      } else if (email) {
        // 回退到邮件
        window.location.href = `mailto:${email}`;
      } else if (phone) {
        // 回退到电话
        const cleanPhone = phone.replace(/[^\d+\-\s()]/g, '');
        window.location.href = `tel:${cleanPhone}`;
      } else {
        console.warn('ContactButton: No email or phone provided');
      }
    },
    [email, phone, preferredMethod, subject, body]
  );

  // 根据首选方式选择图标
  const getIcon = () => {
    if (!showIcon) return null;

    if (preferredMethod === 'email' && email) {
      return <Mail className="h-4 w-4" />;
    } else if (preferredMethod === 'phone' && phone) {
      return <Phone className="h-4 w-4" />;
    } else if (email) {
      return <Mail className="h-4 w-4" />;
    } else if (phone) {
      return <Phone className="h-4 w-4" />;
    } else {
      return <MessageCircle className="h-4 w-4" />;
    }
  };

  const icon = getIcon();

  // 生成无障碍标签
  const getAriaLabel = () => {
    if (preferredMethod === 'email' && email) {
      return `Send email to ${email}`;
    } else if (preferredMethod === 'phone' && phone) {
      return `Call ${phone}`;
    } else if (email) {
      return `Send email to ${email}`;
    } else if (phone) {
      return `Call ${phone}`;
    } else {
      return 'Contact us';
    }
  };

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={getAriaLabel()}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}
