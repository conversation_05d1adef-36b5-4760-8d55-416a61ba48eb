/**
 * 错误日志记录工具
 * 提供统一的错误记录和报告功能
 */

export interface ErrorLogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info';
  message: string;
  stack?: string;
  context?: Record<string, unknown>;
  userAgent?: string;
  url?: string;
  userId?: string;
  sessionId?: string;
}

export interface ErrorReportConfig {
  enableConsoleLog: boolean;
  enableLocalStorage: boolean;
  enableRemoteLogging: boolean;
  maxLocalStorageEntries: number;
  remoteEndpoint?: string;
  apiKey?: string;
}

class ErrorLogger {
  private config: ErrorReportConfig;
  private sessionId: string;

  constructor(config: Partial<ErrorReportConfig> = {}) {
    this.config = {
      enableConsoleLog: true,
      enableLocalStorage: true,
      enableRemoteLogging: false,
      maxLocalStorageEntries: 100,
      ...config,
    };

    // 生成会话ID
    this.sessionId = this.generateSessionId();

    // 设置全局错误处理
    this.setupGlobalErrorHandlers();
  }

  /**
   * 生成唯一的会话ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    if (typeof window === 'undefined') return;

    // 捕获未处理的JavaScript错误
    window.addEventListener('error', (event) => {
      this.logError({
        message: event.message,
        stack: event.error?.stack,
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          type: 'javascript_error',
        },
      });
    });

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        context: {
          type: 'unhandled_promise_rejection',
          reason: event.reason,
        },
      });
    });

    // 捕获资源加载错误
    window.addEventListener(
      'error',
      (event) => {
        if (event.target !== window) {
          this.logError({
            message: `Resource loading error: ${(event.target as HTMLElement)?.tagName}`,
            context: {
              type: 'resource_error',
              tagName: (event.target as HTMLElement)?.tagName,
              src:
                (event.target as HTMLImageElement)?.src ||
                (event.target as HTMLScriptElement)?.src,
            },
          });
        }
      },
      true
    );
  }

  /**
   * 记录错误
   */
  public logError(error: {
    message: string;
    stack?: string;
    context?: Record<string, unknown>;
    level?: 'error' | 'warn' | 'info';
  }): void {
    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level: error.level || 'error',
      message: error.message,
      stack: error.stack,
      context: error.context,
      userAgent:
        typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      sessionId: this.sessionId,
    };

    // 控制台记录
    if (this.config.enableConsoleLog) {
      this.logToConsole(entry);
    }

    // 本地存储记录
    if (this.config.enableLocalStorage) {
      this.logToLocalStorage(entry);
    }

    // 远程记录
    if (this.config.enableRemoteLogging) {
      this.logToRemote(entry);
    }
  }

  /**
   * 记录React错误
   */
  public logReactError(error: Error, errorInfo: React.ErrorInfo): void {
    this.logError({
      message: `React Error: ${error.message}`,
      stack: error.stack,
      context: {
        type: 'react_error',
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
      },
    });
  }

  /**
   * 记录API错误
   */
  public logApiError(error: {
    url: string;
    method: string;
    status?: number;
    message: string;
    response?: unknown;
  }): void {
    this.logError({
      message: `API Error: ${error.message}`,
      context: {
        type: 'api_error',
        url: error.url,
        method: error.method,
        status: error.status,
        response: error.response,
      },
    });
  }

  /**
   * 控制台记录
   */
  private logToConsole(entry: ErrorLogEntry): void {
    const logMethod =
      entry.level === 'error'
        ? console.error
        : entry.level === 'warn'
          ? console.warn
          : console.log;

    logMethod(`[${entry.timestamp}] ${entry.message}`, {
      id: entry.id,
      stack: entry.stack,
      context: entry.context,
    });
  }

  /**
   * 本地存储记录
   */
  private logToLocalStorage(entry: ErrorLogEntry): void {
    if (typeof window === 'undefined') return;

    try {
      const storageKey = 'error_logs';
      const existingLogs = JSON.parse(localStorage.getItem(storageKey) || '[]');

      // 添加新日志
      existingLogs.push(entry);

      // 限制存储数量
      if (existingLogs.length > this.config.maxLocalStorageEntries) {
        existingLogs.splice(
          0,
          existingLogs.length - this.config.maxLocalStorageEntries
        );
      }

      localStorage.setItem(storageKey, JSON.stringify(existingLogs));
    } catch (error) {
      console.warn('Failed to save error log to localStorage:', error);
    }
  }

  /**
   * 远程记录
   */
  private async logToRemote(entry: ErrorLogEntry): Promise<void> {
    if (!this.config.remoteEndpoint) return;

    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.config.apiKey && {
            Authorization: `Bearer ${this.config.apiKey}`,
          }),
        },
        body: JSON.stringify(entry),
      });
    } catch (error) {
      console.warn('Failed to send error log to remote endpoint:', error);
    }
  }

  /**
   * 获取本地存储的错误日志
   */
  public getLocalLogs(): ErrorLogEntry[] {
    if (typeof window === 'undefined') return [];

    try {
      const storageKey = 'error_logs';
      return JSON.parse(localStorage.getItem(storageKey) || '[]');
    } catch (error) {
      console.warn('Failed to retrieve error logs from localStorage:', error);
      return [];
    }
  }

  /**
   * 清除本地存储的错误日志
   */
  public clearLocalLogs(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.removeItem('error_logs');
    } catch (error) {
      console.warn('Failed to clear error logs from localStorage:', error);
    }
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<ErrorReportConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// 创建全局错误记录器实例
export const errorLogger = new ErrorLogger({
  enableConsoleLog: true,
  enableLocalStorage: true,
  enableRemoteLogging: process.env.NODE_ENV === 'production',
  maxLocalStorageEntries: 50,
  // 在生产环境中，可以配置远程端点
  // remoteEndpoint: process.env.NEXT_PUBLIC_ERROR_ENDPOINT,
  // apiKey: process.env.NEXT_PUBLIC_ERROR_API_KEY,
});

// 导出便捷函数
export const logError = (
  message: string,
  context?: Record<string, unknown>
) => {
  errorLogger.logError({ message, context });
};

export const logWarning = (
  message: string,
  context?: Record<string, unknown>
) => {
  errorLogger.logError({ message, context, level: 'warn' });
};

export const logInfo = (message: string, context?: Record<string, unknown>) => {
  errorLogger.logError({ message, context, level: 'info' });
};
