{"name": "tucsenberg-web-1.0", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:useeffect": "eslint src/ --ext .ts,.tsx", "lint:ai-patterns": "npm run lint:useeffect", "typecheck": "tsc --noEmit", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "test:ui": "vitest --ui", "test:components": "vitest run __tests__/components", "test:integration": "vitest run __tests__/integration", "test:static": "vitest run __tests__/pages/static-generation.test.ts", "test:boundaries": "npm run test:static && npm run test:components", "check:ai-patterns": "npm run lint:ai-patterns", "check:all": "npm run typecheck && npm run lint && npm run lint:ai-patterns && npm run test", "check:build": "npm run typecheck && npm run lint && npm run lint:ai-patterns && npm run test:boundaries && npm run build", "validate:build": "tsx scripts/validate-build.ts", "monitor:actions": "tsx scripts/monitor-github-actions.ts", "deploy:check": "tsx scripts/deploy-check.ts", "format:safe": "tsx scripts/format-manager.ts", "format:check": "tsx scripts/format-manager.ts -- --dry-run", "format:backup": "tsx scripts/format-manager.ts backup", "format:validate": "tsx scripts/format-manager.ts validate", "format:restore": "tsx scripts/format-manager.ts restore", "config:check": "tsx scripts/config-file-monitor.ts", "config:watch": "tsx scripts/config-file-monitor.ts watch", "config:restore": "tsx scripts/config-file-monitor.ts restore", "demo:formatting": "node scripts/demo-formatting.js", "demo:complete": "node scripts/demo-complete-toolchain.js", "security:validate": "tsx scripts/validate-security.ts", "security:check": "npm run security:validate", "validate:translations": "npx lingo.dev status", "check:i18n": "npx lingo.dev show", "i18n:translate": "npx lingo.dev i18n", "i18n:status": "npx lingo.dev status", "i18n:ci": "npx lingo.dev ci", "i18n:verify": "tsx scripts/verify-gemini-integration.ts", "prepare": "husky"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@mdx-js/loader": "^3.1.0", "@mdx-js/react": "^3.1.0", "@next/mdx": "^15.3.3", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@t3-oss/env-nextjs": "^0.13.6", "@tanstack/react-query": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.16.0", "lucide-react": "^0.513.0", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "tailwind-merge": "^3.3.0", "tsx": "^4.19.4", "zod": "^3.25.56", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@google/generative-ai": "^0.24.1", "@tailwindcss/postcss": "^4", "@tanstack/react-query-devtools": "^5.80.6", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.1.4", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.3", "eslint-plugin-react-you-might-not-need-an-effect": "^0.1.4", "husky": "^9.1.7", "jsdom": "^26.0.0", "lingo.dev": "^0.99.0", "lint-staged": "^15.3.0", "prettier": "^3.4.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "vitest": "^3.1.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "npm run lint:ai-patterns", "npm run typecheck"], "src/**/*.{js,jsx,ts,tsx}": ["prettier --write"], "__tests__/**/*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{css,scss}": ["prettier --write"]}}