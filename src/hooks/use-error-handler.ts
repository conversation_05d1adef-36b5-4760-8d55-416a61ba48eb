'use client';

import { useCallback } from 'react';
import { errorLogger } from '@/lib/error/error-logger';

/**
 * 错误处理Hook
 * 提供统一的错误处理功能
 */
export function useErrorHandler() {
  /**
   * 处理异步操作错误
   */
  const handleAsyncError = useCallback(
    (error: Error, context?: Record<string, unknown>) => {
      errorLogger.logError({
        message: error.message,
        stack: error.stack,
        context: {
          type: 'async_error',
          ...context,
        },
      });
    },
    []
  );

  /**
   * 处理API错误
   */
  const handleApiError = useCallback(
    (error: {
      url: string;
      method: string;
      status?: number;
      message: string;
      response?: unknown;
    }) => {
      errorLogger.logApiError(error);
    },
    []
  );

  /**
   * 处理表单错误
   */
  const handleFormError = useCallback(
    (error: Error, formData?: Record<string, unknown>) => {
      errorLogger.logError({
        message: `Form Error: ${error.message}`,
        stack: error.stack,
        context: {
          type: 'form_error',
          formData,
        },
      });
    },
    []
  );

  /**
   * 处理网络错误
   */
  const handleNetworkError = useCallback(
    (error: Error, requestInfo?: Record<string, unknown>) => {
      errorLogger.logError({
        message: `Network Error: ${error.message}`,
        stack: error.stack,
        context: {
          type: 'network_error',
          ...requestInfo,
        },
      });
    },
    []
  );

  /**
   * 处理用户操作错误
   */
  const handleUserActionError = useCallback(
    (action: string, error: Error, context?: Record<string, unknown>) => {
      errorLogger.logError({
        message: `User Action Error [${action}]: ${error.message}`,
        stack: error.stack,
        context: {
          type: 'user_action_error',
          action,
          ...context,
        },
      });
    },
    []
  );

  /**
   * 安全执行异步函数
   */
  const safeAsync = useCallback(
    async <T>(
      asyncFn: () => Promise<T>,
      onError?: (error: Error) => void,
      context?: Record<string, unknown>
    ): Promise<T | null> => {
      try {
        return await asyncFn();
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        handleAsyncError(err, context);
        onError?.(err);
        return null;
      }
    },
    [handleAsyncError]
  );

  /**
   * 安全执行同步函数
   */
  const safeSync = useCallback(
    <T>(
      syncFn: () => T,
      onError?: (error: Error) => void,
      context?: Record<string, unknown>
    ): T | null => {
      try {
        return syncFn();
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        errorLogger.logError({
          message: err.message,
          stack: err.stack,
          context: {
            type: 'sync_error',
            ...context,
          },
        });
        onError?.(err);
        return null;
      }
    },
    []
  );

  /**
   * 创建错误边界触发器
   */
  const throwError = useCallback(
    (message: string, context?: Record<string, unknown>) => {
      const error = new Error(message);
      errorLogger.logError({
        message,
        stack: error.stack,
        context: {
          type: 'manual_error',
          ...context,
        },
      });
      throw error;
    },
    []
  );

  return {
    handleAsyncError,
    handleApiError,
    handleFormError,
    handleNetworkError,
    handleUserActionError,
    safeAsync,
    safeSync,
    throwError,
  };
}

/**
 * API错误处理Hook
 */
export function useApiErrorHandler() {
  const { handleApiError } = useErrorHandler();

  const handleFetchError = useCallback(
    async (url: string, options: RequestInit = {}) => {
      try {
        const response = await fetch(url, options);

        if (!response.ok) {
          const errorData = await response.text().catch(() => 'Unknown error');
          handleApiError({
            url,
            method: options.method || 'GET',
            status: response.status,
            message: `HTTP ${response.status}: ${response.statusText}`,
            response: errorData,
          });
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        if (error instanceof TypeError && error.message.includes('fetch')) {
          // 网络错误
          handleApiError({
            url,
            method: options.method || 'GET',
            message: 'Network error: Failed to fetch',
          });
        }
        throw error;
      }
    },
    [handleApiError]
  );

  return {
    handleFetchError,
    handleApiError,
  };
}

/**
 * 表单错误处理Hook
 */
export function useFormErrorHandler() {
  const { handleFormError } = useErrorHandler();

  const validateAndHandle = useCallback(
    <T>(validator: () => T, formData?: Record<string, unknown>): T | null => {
      try {
        return validator();
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        handleFormError(err, formData);
        return null;
      }
    },
    [handleFormError]
  );

  return {
    validateAndHandle,
    handleFormError,
  };
}
