import { redirect } from 'next/navigation';
import { headers } from 'next/headers';
import { defaultLocale } from '@/lib/i18n/config';

export default async function RootPage() {
  const headersList = await headers();
  const acceptLanguage = headersList.get('accept-language') || '';

  // 简单的语言检测
  const preferredLocale = acceptLanguage.includes('zh') ? 'zh' : defaultLocale;

  // 重定向到对应的语言版本
  redirect(`/${preferredLocale}`);
}
