'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { LanguageSwitcher } from './language-switcher';
import { ThemeToggle } from './theme-toggle';
import { DropdownMenu, MobileDropdownMenu } from './dropdown-menu';
import { navigationItems, getLocalizedHref } from '@/lib/i18n/navigation';
import { type Locale } from '@/lib/i18n/config';
import { cn } from '@/lib/utils';
import { Menu, X } from 'lucide-react';
import { useState } from 'react';

interface NavigationProps {
  locale: Locale;
}

export function Navigation({ locale }: NavigationProps) {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  const isActive = (href: string) => {
    const localizedHref = getLocalizedHref(href, locale);
    if (localizedHref === `/${locale}` && pathname === `/${locale}`) {
      return true;
    }
    if (localizedHref !== `/${locale}` && pathname.startsWith(localizedHref)) {
      return true;
    }
    return false;
  };

  return (
    <nav className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link
            href={getLocalizedHref('/', locale)}
            className="flex items-center space-x-2"
          >
            <div className="h-8 w-8 rounded-lg bg-gradient-to-r from-blue-600 to-purple-600" />
            <span className="font-bold text-xl">Tucsenberg</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2">
            {navigationItems.map((item) => {
              const active = isActive(item.href);

              return (
                <DropdownMenu
                  key={item.href}
                  item={item}
                  locale={locale}
                  className={cn(
                    'text-sm font-medium transition-colors',
                    active
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-primary'
                  )}
                />
              );
            })}
          </div>

          {/* Actions */}
          <div className="flex items-center space-x-2">
            <ThemeToggle />
            <LanguageSwitcher currentLocale={locale} />

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={handleMobileMenuToggle}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t bg-background/95 backdrop-blur animate-in slide-in-from-top-2 duration-200">
            <div className="py-2">
              {navigationItems.map((item) => (
                <MobileDropdownMenu
                  key={item.href}
                  item={item}
                  locale={locale}
                  onItemClick={handleMobileMenuClose}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
