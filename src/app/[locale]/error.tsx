'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react';
import { type Locale } from '@/lib/i18n/config';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

/**
 * 全局错误页面
 * Next.js App Router的错误处理页面
 */
export default function ErrorPage({ error, reset }: ErrorPageProps) {
  useEffect(() => {
    // 记录错误到控制台
    console.error('Global error caught:', error);

    // 在生产环境中，可以将错误发送到监控服务
    if (process.env.NODE_ENV === 'production') {
      // 示例：发送到错误监控服务
      // logErrorToService(error);
    }
  }, [error]);

  // 尝试从URL获取语言信息
  const getLocaleFromPath = (): Locale => {
    if (typeof window !== 'undefined') {
      const path = window.location.pathname;
      if (path.startsWith('/zh')) return 'zh';
    }
    return 'en';
  };

  const locale = getLocaleFromPath();

  const content = {
    en: {
      title: 'Something went wrong!',
      description:
        'We encountered an unexpected error. Our team has been notified.',
      tryAgain: 'Try again',
      goHome: 'Go to Homepage',
      reportBug: 'Report Bug',
      errorDetails: 'Error Details',
      errorId: 'Error ID',
      timestamp: 'Timestamp',
      whatHappened: 'What happened?',
      whatHappenedText:
        'An unexpected error occurred while processing your request. This could be due to a temporary issue or a bug in our system.',
      whatCanYouDo: 'What can you do?',
      whatCanYouDoSteps: [
        'Try refreshing the page',
        'Go back to the homepage',
        'Contact support if the problem persists',
        'Report this bug to help us improve',
      ],
    },
    zh: {
      title: '出现了一些问题！',
      description: '我们遇到了一个意外错误。我们的团队已经收到通知。',
      tryAgain: '重试',
      goHome: '返回首页',
      reportBug: '报告错误',
      errorDetails: '错误详情',
      errorId: '错误ID',
      timestamp: '时间戳',
      whatHappened: '发生了什么？',
      whatHappenedText:
        '在处理您的请求时发生了意外错误。这可能是由于临时问题或系统中的错误造成的。',
      whatCanYouDo: '您可以做什么？',
      whatCanYouDoSteps: [
        '尝试刷新页面',
        '返回首页',
        '如果问题持续存在，请联系支持',
        '报告此错误以帮助我们改进',
      ],
    },
  };

  const currentContent = content[locale];
  const errorId = error.digest || 'unknown';
  const timestamp = new Date().toLocaleString(
    locale === 'zh' ? 'zh-CN' : 'en-US'
  );

  const handleReportBug = () => {
    // 构建错误报告邮件
    const subject = encodeURIComponent(
      `Bug Report: ${error.message || 'Unknown Error'}`
    );
    const body = encodeURIComponent(`
Error Details:
- Error ID: ${errorId}
- Timestamp: ${timestamp}
- Message: ${error.message || 'No message'}
- Stack: ${error.stack || 'No stack trace'}
- URL: ${window.location.href}
- User Agent: ${navigator.userAgent}

Please describe what you were doing when this error occurred:
[Your description here]
    `);

    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-950 dark:to-orange-950 p-4">
      <div className="w-full max-w-2xl space-y-6">
        {/* 主要错误卡片 */}
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 p-3 bg-red-100 dark:bg-red-900 rounded-full w-fit">
              <AlertTriangle className="h-8 w-8 text-red-600 dark:text-red-400" />
            </div>
            <CardTitle className="text-2xl text-red-800 dark:text-red-200">
              {currentContent.title}
            </CardTitle>
            <CardDescription className="text-red-600 dark:text-red-300">
              {currentContent.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 操作按钮 */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              <Button onClick={reset} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                {currentContent.tryAgain}
              </Button>
              <Button
                onClick={() =>
                  (window.location.href = `/${locale === 'en' ? '' : locale}`)
                }
                variant="outline"
                className="w-full"
              >
                <Home className="mr-2 h-4 w-4" />
                {currentContent.goHome}
              </Button>
              <Button
                onClick={handleReportBug}
                variant="secondary"
                className="w-full"
              >
                <Bug className="mr-2 h-4 w-4" />
                {currentContent.reportBug}
              </Button>
            </div>

            {/* 帮助信息 */}
            <div className="grid md:grid-cols-2 gap-6 pt-4 border-t">
              <div>
                <h3 className="font-semibold mb-2">
                  {currentContent.whatHappened}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {currentContent.whatHappenedText}
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">
                  {currentContent.whatCanYouDo}
                </h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  {currentContent.whatCanYouDoSteps.map((step, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <span className="text-xs mt-1">•</span>
                      {step}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 错误详情（仅开发环境） */}
        {process.env.NODE_ENV === 'development' && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">
                {currentContent.errorDetails}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <strong>{currentContent.errorId}:</strong>
                <code className="ml-2 text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                  {errorId}
                </code>
              </div>
              <div>
                <strong>{currentContent.timestamp}:</strong>
                <span className="ml-2 text-sm">{timestamp}</span>
              </div>
              {error.message && (
                <div>
                  <strong>Message:</strong>
                  <pre className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto">
                    {error.message}
                  </pre>
                </div>
              )}
              {error.stack && (
                <div>
                  <strong>Stack Trace:</strong>
                  <pre className="mt-2 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs overflow-auto max-h-40">
                    {error.stack}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
