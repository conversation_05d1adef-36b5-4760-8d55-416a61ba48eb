// 🧪 完整工具链演示文件
import React, { useState, useEffect } from 'react';
interface Props {
  title: string;
  items: string[];
  onSelect?: (item: string) => void;
}
const DemoComponent: React.FC<Props> = ({ title, items, onSelect }) => {
  const [selected, setSelected] = useState<string | null>(null);
  const [count, setCount] = useState(0);
  useEffect(() => {
    console.log('Component mounted');
  }, []);
  const handleClick = (item: string) => {
    setSelected(item);
    if (onSelect) {
      onSelect(item);
    }
    setCount((prev) => prev + 1);
  };
  return (
    <div style={{ padding: '20px', border: '1px solid #ccc' }}>
      <h2 style={{ color: 'blue', fontSize: '18px' }}>{title}</h2>
      <ul style={{ listStyle: 'none', padding: 0 }}>
        {items.map((item, index) => (
          <li
            key={index}
            onClick={() => handleClick(item)}
            style={{
              cursor: 'pointer',
              padding: '5px',
              backgroundColor: selected === item ? '#f0f0f0' : 'white',
            }}
          >
            {item}
          </li>
        ))}
      </ul>
      <p>
        Selected: {selected || 'None'} (Clicks: {count})
      </p>
    </div>
  );
};
export default DemoComponent;
