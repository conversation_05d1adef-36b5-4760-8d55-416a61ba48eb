import { describe, it, expect, vi } from 'vitest';
import { renderWithProviders, screen } from '@/lib/testing/utils';
import { Navigation } from '@/components/navigation/navigation';

// Mock next/navigation
vi.mock('next/navigation', () => ({
  usePathname: () => '/en',
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

describe('Navigation Component', () => {
  it('renders navigation with English locale', () => {
    renderWithProviders(<Navigation locale="en" />);

    expect(screen.getByText('Tucsenberg')).toBeInTheDocument();
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Products')).toBeInTheDocument();
    expect(screen.getByText('Blog')).toBeInTheDocument();
    expect(screen.getByText('About')).toBeInTheDocument();
  });

  it('renders navigation with Chinese locale', () => {
    renderWithProviders(<Navigation locale="zh" />);

    expect(screen.getByText('Tucsenberg')).toBeInTheDocument();
    expect(screen.getByText('首页')).toBeInTheDocument();
    expect(screen.getByText('产品')).toBeInTheDocument();
    expect(screen.getByText('博客')).toBeInTheDocument();
    expect(screen.getByText('关于')).toBeInTheDocument();
  });

  it('displays language switcher', () => {
    renderWithProviders(<Navigation locale="en" />);

    // Language switcher should be present
    expect(screen.getByRole('button', { name: /EN/i })).toBeInTheDocument();
  });

  it('displays theme toggle', () => {
    renderWithProviders(<Navigation locale="en" />);

    // Theme toggle button should be present
    const themeButton = screen.getByRole('button', { name: /toggle theme/i });
    expect(themeButton).toBeInTheDocument();
  });

  it('shows mobile menu button on mobile', () => {
    renderWithProviders(<Navigation locale="en" />);

    // Mobile menu button should be present (though hidden on desktop)
    const mobileMenuButtons = screen.getAllByRole('button');
    expect(mobileMenuButtons.length).toBeGreaterThan(0);
  });
});
