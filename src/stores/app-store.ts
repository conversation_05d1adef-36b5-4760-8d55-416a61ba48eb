import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'system';

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
}

interface AppState {
  // 主题状态
  theme: Theme;

  // 用户状态
  user: User | null;

  // UI 状态
  sidebarOpen: boolean;

  // 计算属性
  isAuthenticated: boolean;

  // 操作方法
  setTheme: (theme: Theme) => void;
  setUser: (user: User | null) => void;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;
  logout: () => void;
}

export const useAppStore = create<AppState>()(
  devtools(
    persist(
      (set, get) => ({
        // 初始状态
        theme: 'system',
        user: null,
        sidebarOpen: false,

        // 计算属性
        get isAuthenticated() {
          return get().user !== null;
        },

        // 操作方法
        setTheme: (theme) => set({ theme }, false, 'setTheme'),

        setUser: (user) => set({ user }, false, 'setUser'),

        setSidebarOpen: (sidebarOpen) =>
          set({ sidebarOpen }, false, 'setSidebarOpen'),

        toggleSidebar: () =>
          set(
            (state) => ({ sidebarOpen: !state.sidebarOpen }),
            false,
            'toggleSidebar'
          ),

        logout: () => set({ user: null }, false, 'logout'),
      }),
      {
        name: 'app-store',
        partialize: (state) => ({
          theme: state.theme,
          user: state.user,
        }),
      }
    ),
    {
      name: 'app-store',
    }
  )
);
