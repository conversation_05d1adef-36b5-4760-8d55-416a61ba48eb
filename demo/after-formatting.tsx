// 🧪 格式化演示文件 - 不规范版本
interface User {
  name: string;
  age: number;
  email: string;
  isActive?: boolean;
}
const users: User[] = [
  { name: '<PERSON>', age: 30, email: '<EMAIL>', isActive: true },
  { name: '<PERSON>', age: 25, email: '<EMAIL>' },
];
const getUserById = (id: number): User | undefined => {
  return users.find((user) => user.name.length > id);
};
const config = {
  host: 'localhost',
  port: 3000,
  debug: true,
  features: ['auth', 'api', 'ui'],
};
const processUsers = () => {
  const result = users
    .map((user) => {
      return {
        ...user,
        displayName: user.name.toUpperCase(),
        isAdult: user.age >= 18,
      };
    })
    .filter((user) => user.isAdult && user.isActive);
  return result;
};
export type { User };
export { users, getUserById, config, processUsers };
