'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Trash2,
  Download,
  RefreshCw,
  AlertTriangle,
  Info,
  AlertCircle,
} from 'lucide-react';
import { errorLogger, type ErrorLogEntry } from '@/lib/error/error-logger';

/**
 * 错误日志管理页面
 * 仅在开发环境中可用，用于查看和管理错误日志
 */
export default function ErrorLogsPage() {
  const [logs, setLogs] = useState<ErrorLogEntry[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadLogs();
  }, []);

  // 仅在开发环境中显示
  if (process.env.NODE_ENV === 'production') {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card>
          <CardHeader>
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              This page is only available in development mode.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  const loadLogs = () => {
    setIsLoading(true);
    try {
      const localLogs = errorLogger.getLocalLogs();
      setLogs(localLogs.reverse()); // 最新的在前面
    } catch (error) {
      console.error('Failed to load error logs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    if (confirm('Are you sure you want to clear all error logs?')) {
      errorLogger.clearLocalLogs();
      setLogs([]);
    }
  };

  const downloadLogs = () => {
    const dataStr = JSON.stringify(logs, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `error-logs-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const testError = () => {
    // 测试错误记录
    errorLogger.logError({
      message: 'Test error from error logs page',
      context: {
        type: 'test_error',
        timestamp: new Date().toISOString(),
      },
    });
    loadLogs();
  };

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warn':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'destructive';
      case 'warn':
        return 'secondary';
      case 'info':
        return 'default';
      default:
        return 'outline';
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* 头部 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Error Logs Management
            </CardTitle>
            <CardDescription>
              View and manage application error logs (Development only)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2 flex-wrap">
              <Button onClick={loadLogs} disabled={isLoading}>
                <RefreshCw
                  className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
                />
                Refresh
              </Button>
              <Button onClick={testError} variant="outline">
                Test Error
              </Button>
              <Button
                onClick={downloadLogs}
                variant="outline"
                disabled={logs.length === 0}
              >
                <Download className="mr-2 h-4 w-4" />
                Download
              </Button>
              <Button
                onClick={clearLogs}
                variant="destructive"
                disabled={logs.length === 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Clear All
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{logs.length}</div>
              <div className="text-sm text-muted-foreground">Total Logs</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-red-500">
                {logs.filter((log) => log.level === 'error').length}
              </div>
              <div className="text-sm text-muted-foreground">Errors</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-yellow-500">
                {logs.filter((log) => log.level === 'warn').length}
              </div>
              <div className="text-sm text-muted-foreground">Warnings</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-500">
                {logs.filter((log) => log.level === 'info').length}
              </div>
              <div className="text-sm text-muted-foreground">Info</div>
            </CardContent>
          </Card>
        </div>

        {/* 错误日志列表 */}
        <Card>
          <CardHeader>
            <CardTitle>Error Logs</CardTitle>
            <CardDescription>
              {logs.length === 0
                ? 'No error logs found'
                : `Showing ${logs.length} log entries`}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-8">Loading...</div>
            ) : logs.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No error logs found. Try triggering a test error.
              </div>
            ) : (
              <div className="space-y-4">
                {logs.map((log) => (
                  <Card key={log.id} className="border-l-4 border-l-red-500">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between gap-4">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            {getLevelIcon(log.level)}
                            <Badge
                              variant={
                                getLevelColor(log.level) as
                                  | 'default'
                                  | 'destructive'
                                  | 'outline'
                                  | 'secondary'
                              }
                            >
                              {log.level.toUpperCase()}
                            </Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(log.timestamp).toLocaleString()}
                            </span>
                          </div>
                          <div className="font-medium">{log.message}</div>
                          {log.context && (
                            <div className="text-sm text-muted-foreground">
                              <strong>Context:</strong>{' '}
                              {JSON.stringify(log.context, null, 2)}
                            </div>
                          )}
                          {log.stack && (
                            <details className="text-xs">
                              <summary className="cursor-pointer text-muted-foreground">
                                Stack Trace
                              </summary>
                              <pre className="mt-2 p-2 bg-muted rounded overflow-auto">
                                {log.stack}
                              </pre>
                            </details>
                          )}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          ID: {log.id}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
