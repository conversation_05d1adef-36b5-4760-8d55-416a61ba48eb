import '@testing-library/jest-dom';
import { cleanup } from '@testing-library/react';
import { afterEach, beforeAll, vi } from 'vitest';

// Mock Next.js navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
}));

// Mock翻译系统
vi.mock('@/lib/i18n/use-translations', () => {
  // 创建Mock翻译数据
  const mockTranslations = {
    home: {
      title: 'Tucsenberg Web 1.0',
      subtitle:
        'Modern frontend technology solution - Based on Next.js 15 and React 19',
      getStarted: 'Get Started',
      learnMore: 'Learn More',
      toggleTheme: 'Toggle Theme',
      configComplete: 'Tech Stack Configuration Complete ✅',
      configDescription:
        'Project has successfully configured the following core features',
      features: {
        nextjs: {
          title: 'Next.js 15 + React 19',
          description: 'Latest React and Next.js technology stack',
        },
        ui: {
          title: 'shadcn/ui + Tailwind CSS',
          description: 'Modern design system and styling framework',
        },
        state: {
          title: 'Zustand + React Query',
          description: 'Efficient state management and data fetching',
        },
        typescript: {
          title: 'TypeScript + ESLint',
          description: 'Type safety and code quality assurance',
        },
        testing: {
          title: 'Vitest + Testing Library',
          description: 'Complete testing solution',
        },
        ai: {
          title: 'AI Anti-pattern Protection',
          description: 'Avoid common AI-generated errors',
        },
      },
      checklist: [
        'Next.js 15 + React 19',
        'shadcn/ui + Tailwind CSS',
        'Zustand + React Query',
        'TypeScript + ESLint',
        'Vitest + Testing Library',
        'AI Anti-pattern Protection',
      ],
    },
    common: {
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
    },
    navigation: {
      home: 'Home',
      products: 'Products',
      about: 'About',
      contact: 'Contact',
    },
  };

  // 创建Mock翻译函数，既是函数又有属性
  const createMockTranslationFunction = () => {
    const translationFunction = (key: string) => {
      const translations: Record<string, string> = {
        'home.features.nextjs.title': 'Next.js 15 + React 19',
        'home.features.nextjs.description':
          'Latest React and Next.js technology stack',
        'home.features.ui.title': 'shadcn/ui + Tailwind CSS',
        'home.features.ui.description':
          'Modern design system and styling framework',
        'home.features.state.title': 'Zustand + React Query',
        'home.features.state.description':
          'Efficient state management and data fetching',
        'home.features.typescript.title': 'TypeScript + ESLint',
        'home.features.typescript.description':
          'Type safety and code quality assurance',
        'home.features.testing.title': 'Vitest + Testing Library',
        'home.features.testing.description': 'Complete testing solution',
        'home.features.ai.title': 'AI Anti-pattern Protection',
        'home.features.ai.description': 'Avoid common AI-generated errors',
      };
      return translations[key] || key;
    };

    // 添加属性
    Object.assign(translationFunction, mockTranslations);

    return translationFunction;
  };

  return {
    getServerTranslations: vi
      .fn()
      .mockImplementation(() => createMockTranslationFunction()),
    useClientTranslations: vi
      .fn()
      .mockImplementation(() => createMockTranslationFunction()),
    createTranslationFunction: vi
      .fn()
      .mockImplementation(() => createMockTranslationFunction()),
  };
});

// 每个测试后清理
afterEach(() => {
  cleanup();
});

// 全局设置
beforeAll(() => {
  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock scrollTo
  window.scrollTo = vi.fn();

  // Mock HTMLFormElement.submit
  HTMLFormElement.prototype.submit = vi.fn();
});
