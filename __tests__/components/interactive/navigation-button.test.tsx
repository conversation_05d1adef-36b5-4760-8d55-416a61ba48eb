import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  NavigationButton,
  ExternalLinkButton,
  LocalizedNavigationButton,
} from '@/components/interactive/navigation-button';

// Mock Next.js router
const mockPush = vi.fn();
const mockPathname = '/en';

vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  }),
  usePathname: () => mockPathname,
}));

// Mock i18n config
vi.mock('@/lib/i18n/config', () => ({
  defaultLocale: 'en',
  locales: ['en', 'zh'],
  getLocaleFromPathname: (pathname: string) => {
    if (pathname.startsWith('/zh')) return 'zh';
    if (pathname.startsWith('/en')) return 'en';
    return 'en';
  },
}));

// Mock window.location and window.open
const mockLocation = {
  href: '',
  assign: vi.fn(),
  replace: vi.fn(),
  reload: vi.fn(),
};

const mockOpen = vi.fn();

Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
});

Object.defineProperty(window, 'open', {
  value: mockOpen,
  writable: true,
});

describe('NavigationButton', () => {
  beforeEach(() => {
    mockLocation.href = '';
    mockPush.mockClear();
    mockOpen.mockClear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(<NavigationButton href="/products">View Products</NavigationButton>);

    expect(screen.getByText('View Products')).toBeInTheDocument();
  });

  it('navigates to internal route using router.push', async () => {
    const user = userEvent.setup();

    render(<NavigationButton href="/products">View Products</NavigationButton>);

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockPush).toHaveBeenCalledWith('/products');
  });

  it('opens external link in same tab by default', async () => {
    const user = userEvent.setup();

    render(
      <NavigationButton href="https://example.com">
        External Link
      </NavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('https://example.com');
  });

  it('opens external link in new tab when newTab is true', async () => {
    const user = userEvent.setup();

    render(
      <NavigationButton href="https://example.com" newTab>
        External Link
      </NavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockOpen).toHaveBeenCalledWith(
      'https://example.com',
      '_blank',
      'noopener,noreferrer'
    );
  });

  it('shows external icon for external links', () => {
    render(
      <NavigationButton href="https://example.com" showExternalIcon>
        External Link
      </NavigationButton>
    );

    // Check for ExternalLink icon (lucide-react)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });

  it('uses window.location when useClientRouting is false', async () => {
    const user = userEvent.setup();

    render(
      <NavigationButton href="/products" useClientRouting={false}>
        View Products
      </NavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('/products');
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('has proper accessibility attributes for internal links', () => {
    render(<NavigationButton href="/products">View Products</NavigationButton>);

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Navigate to /products');
  });

  it('has proper accessibility attributes for external links', () => {
    render(
      <NavigationButton href="https://example.com" newTab>
        External Link
      </NavigationButton>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute(
      'aria-label',
      'Open https://example.com in new tab'
    );
  });
});

describe('ExternalLinkButton', () => {
  beforeEach(() => {
    mockOpen.mockClear();
    mockLocation.href = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(
      <ExternalLinkButton href="https://example.com">
        Visit Website
      </ExternalLinkButton>
    );

    expect(screen.getByText('Visit Website')).toBeInTheDocument();
  });

  it('opens in new tab by default', async () => {
    const user = userEvent.setup();

    render(
      <ExternalLinkButton href="https://example.com">
        Visit Website
      </ExternalLinkButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockOpen).toHaveBeenCalledWith(
      'https://example.com',
      '_blank',
      'noopener,noreferrer'
    );
  });

  it('opens in same tab when newTab is false', async () => {
    const user = userEvent.setup();

    render(
      <ExternalLinkButton href="https://example.com" newTab={false}>
        Visit Website
      </ExternalLinkButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockLocation.href).toBe('https://example.com');
  });

  it('shows external icon by default', () => {
    render(
      <ExternalLinkButton href="https://example.com">
        Visit Website
      </ExternalLinkButton>
    );

    // Check for ExternalLink icon (lucide-react)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });
});

describe('LocalizedNavigationButton', () => {
  beforeEach(() => {
    mockPush.mockClear();
    mockLocation.href = '';
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders with correct text', () => {
    render(
      <LocalizedNavigationButton href="/products">
        View Products
      </LocalizedNavigationButton>
    );

    expect(screen.getByText('View Products')).toBeInTheDocument();
  });

  it('preserves current locale by default', async () => {
    const user = userEvent.setup();

    render(
      <LocalizedNavigationButton href="/products">
        View Products
      </LocalizedNavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    // For default locale (en), no prefix is added
    expect(mockPush).toHaveBeenCalledWith('/products');
  });

  it('uses target locale when specified', async () => {
    const user = userEvent.setup();

    render(
      <LocalizedNavigationButton
        href="/products"
        targetLocale="zh"
        preserveLocale={false}
      >
        View Products
      </LocalizedNavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockPush).toHaveBeenCalledWith('/zh/products');
  });

  it('navigates without locale when preserveLocale is false', async () => {
    const user = userEvent.setup();

    render(
      <LocalizedNavigationButton href="/products" preserveLocale={false}>
        View Products
      </LocalizedNavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    expect(mockPush).toHaveBeenCalledWith('/products');
  });

  it('uses window.location when useClientRouting is false', async () => {
    const user = userEvent.setup();

    render(
      <LocalizedNavigationButton href="/products" useClientRouting={false}>
        View Products
      </LocalizedNavigationButton>
    );

    const button = screen.getByRole('button');
    await user.click(button);

    // For default locale (en), no prefix is added
    expect(mockLocation.href).toBe('/products');
    expect(mockPush).not.toHaveBeenCalled();
  });

  it('shows navigation icon when showIcon is true', () => {
    render(
      <LocalizedNavigationButton href="/products" showIcon>
        View Products
      </LocalizedNavigationButton>
    );

    // Check for ArrowRight icon (lucide-react)
    expect(document.querySelector('svg')).toBeInTheDocument();
  });
});
