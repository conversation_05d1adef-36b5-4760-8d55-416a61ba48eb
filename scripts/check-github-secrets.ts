#!/usr/bin/env tsx

/**
 * GitHub Secrets 检查脚本
 * 用于验证 CI/CD 环境中的必需环境变量
 */

function checkGitHubSecrets(): void {
  console.log('🔍 检查 GitHub Secrets 配置...\n');

  const requiredSecrets = [
    'GOOGLE_API_KEY',
    'LINGODOTDEV_API_KEY', // 可选，但推荐
  ];

  const missingSecrets: string[] = [];
  const presentSecrets: string[] = [];

  requiredSecrets.forEach((secretName) => {
    const value = process.env[secretName];
    if (!value) {
      missingSecrets.push(secretName);
    } else {
      presentSecrets.push(secretName);
      // 只显示前几个字符，保护敏感信息
      const maskedValue = value.substring(0, 8) + '***';
      console.log(`✅ ${secretName}: ${maskedValue}`);
    }
  });

  if (missingSecrets.length > 0) {
    console.log('\n❌ 缺少的 GitHub Secrets:');
    missingSecrets.forEach((secretName) => {
      console.log(`  - ${secretName}`);
    });

    console.log('\n💡 请在 GitHub 仓库设置中添加这些 Secrets:');
    console.log('   1. 访问 GitHub 仓库页面');
    console.log('   2. 点击 Settings > Secrets and variables > Actions');
    console.log('   3. 点击 "New repository secret"');
    console.log('   4. 添加所需的 Secret');

    process.exit(1);
  }

  console.log('\n🎉 所有必需的 GitHub Secrets 都已正确配置！');
}

if (require.main === module) {
  checkGitHubSecrets();
}

export { checkGitHubSecrets };
