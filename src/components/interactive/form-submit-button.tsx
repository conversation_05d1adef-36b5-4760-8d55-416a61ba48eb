'use client';

import * as React from 'react';
import { Button } from '@/components/ui/button';
import { Send, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * 专门的表单提交按钮组件
 *
 * 这个组件专门用于处理表单提交，完全封装提交逻辑，
 * 符合 React 19 严格的组件边界规则。
 *
 * @example
 * // 基本表单提交按钮
 * <FormSubmitButton>
 *   提交表单
 * </FormSubmitButton>
 *
 * // 带自定义提交处理的按钮
 * <FormSubmitButton onSubmit={handleSubmit} showIcon>
 *   发送消息
 * </FormSubmitButton>
 *
 * // 联系表单提交按钮
 * <ContactFormSubmitButton
 *   email="<EMAIL>"
 *   subject="Contact Form Submission"
 * >
 *   发送联系信息
 * </ContactFormSubmitButton>
 */

// 安全的 Button props，排除所有函数类型
type SafeButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  | 'onClick'
  | 'onMouseDown'
  | 'onMouseUp'
  | 'onKeyDown'
  | 'onKeyUp'
  | 'onFocus'
  | 'onBlur'
  | 'onSubmit'
  | 'type'
>;

/**
 * 基础表单提交按钮组件
 */
interface FormSubmitButtonProps extends SafeButtonProps {
  /**
   * 自定义提交处理函数（可选）
   */
  onSubmit?: (formData: FormData) => void | Promise<void>;

  /**
   * 是否显示提交图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';

  /**
   * 提交时的加载文本
   */
  loadingText?: string;

  /**
   * 是否在提交时禁用按钮
   */
  disableOnSubmit?: boolean;
}

export function FormSubmitButton({
  onSubmit,
  showIcon = true,
  iconPosition = 'left',
  loadingText,
  disableOnSubmit = true,
  children,
  className,
  disabled,
  ...props
}: FormSubmitButtonProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleClick = React.useCallback(
    async (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (isSubmitting) return;

      // 查找最近的表单元素
      const form = e.currentTarget.closest('form');
      if (!form) {
        console.warn('FormSubmitButton: No form found');
        return;
      }

      if (onSubmit) {
        // 使用自定义提交处理
        setIsSubmitting(true);
        try {
          const formData = new FormData(form);
          await onSubmit(formData);
        } catch (error) {
          console.error('Form submission error:', error);
        } finally {
          setIsSubmitting(false);
        }
      } else {
        // 使用默认表单提交
        form.submit();
      }
    },
    [onSubmit, isSubmitting]
  );

  const isDisabled = disabled || (disableOnSubmit && isSubmitting);

  const icon = showIcon ? (
    isSubmitting ? (
      <Loader2 className="h-4 w-4 animate-spin" />
    ) : (
      <Send className="h-4 w-4" />
    )
  ) : null;

  const displayText = isSubmitting && loadingText ? loadingText : children;

  return (
    <Button
      type="submit"
      onClick={handleClick}
      disabled={isDisabled}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={isSubmitting ? 'Submitting form...' : 'Submit form'}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {displayText}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 联系表单提交按钮组件
 */
interface ContactFormSubmitButtonProps extends SafeButtonProps {
  /**
   * 目标邮箱地址
   */
  email?: string;

  /**
   * 邮件主题
   */
  subject?: string;

  /**
   * 是否显示图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';

  /**
   * 提交时的加载文本
   */
  loadingText?: string;
}

export function ContactFormSubmitButton({
  email = '<EMAIL>',
  subject = 'Contact Form Submission',
  showIcon = true,
  iconPosition = 'left',
  loadingText,
  children,
  className,
  disabled,
  ...props
}: ContactFormSubmitButtonProps) {
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleClick = React.useCallback(
    async (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (isSubmitting) return;

      // 查找最近的表单元素
      const form = e.currentTarget.closest('form');
      if (!form) {
        console.warn('ContactFormSubmitButton: No form found');
        return;
      }

      setIsSubmitting(true);
      try {
        // 获取表单数据
        const formData = new FormData(form);
        const name = formData.get('name') as string;
        const userEmail = formData.get('email') as string;
        const formSubject = (formData.get('subject') as string) || subject;
        const message = formData.get('message') as string;

        // 构建邮件内容
        const emailBody = `
Name: ${name}
Email: ${userEmail}
Subject: ${formSubject}

Message:
${message}
      `.trim();

        // 构建 mailto URL
        const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(formSubject)}&body=${encodeURIComponent(emailBody)}`;

        // 打开邮件客户端
        window.location.href = mailtoUrl;
      } catch (error) {
        console.error('Form submission error:', error);
      } finally {
        setIsSubmitting(false);
      }
    },
    [email, subject, isSubmitting]
  );

  const isDisabled = disabled || isSubmitting;

  const icon = showIcon ? (
    isSubmitting ? (
      <Loader2 className="h-4 w-4 animate-spin" />
    ) : (
      <Send className="h-4 w-4" />
    )
  ) : null;

  const displayText = isSubmitting && loadingText ? loadingText : children;

  return (
    <Button
      type="submit"
      onClick={handleClick}
      disabled={isDisabled}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={isSubmitting ? 'Submitting form...' : 'Submit form'}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {displayText}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 简单的提交按钮组件
 * 用于基本的表单提交，不需要特殊处理
 */
interface SimpleSubmitButtonProps extends SafeButtonProps {
  /**
   * 是否显示图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function SimpleSubmitButton({
  showIcon = true,
  iconPosition = 'left',
  children,
  className,
  ...props
}: SimpleSubmitButtonProps) {
  return (
    <FormSubmitButton
      showIcon={showIcon}
      iconPosition={iconPosition}
      className={className}
      {...props}
    >
      {children}
    </FormSubmitButton>
  );
}
