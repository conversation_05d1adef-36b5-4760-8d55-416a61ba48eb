# 开发环境搭建指南

## 目录
- [系统要求](#系统要求)
- [环境准备](#环境准备)
- [项目安装](#项目安装)
- [开发工具配置](#开发工具配置)
- [环境变量配置](#环境变量配置)
- [验证安装](#验证安装)
- [常见问题](#常见问题)
- [开发工作流](#开发工作流)

## 系统要求

### 基础要求
- **操作系统**：Windows 10+、macOS 10.15+、Linux (Ubuntu 18.04+)
- **Node.js**：18.17.0 或更高版本（推荐 20.x LTS）
- **包管理器**：npm 9+ 或 yarn 1.22+ 或 pnpm 8+
- **Git**：2.30+ 版本
- **内存**：至少 8GB RAM（推荐 16GB）
- **存储**：至少 5GB 可用空间

### 推荐配置
- **CPU**：4核心或更多
- **内存**：16GB RAM 或更多
- **SSD**：固态硬盘（提升构建速度）
- **网络**：稳定的互联网连接（用于依赖下载）

## 环境准备

### 1. 安装 Node.js

#### 方法一：官方安装包
```bash
# 访问 https://nodejs.org/
# 下载并安装 LTS 版本

# 验证安装
node --version  # 应显示 v18.17.0 或更高
npm --version   # 应显示 9.0.0 或更高
```

#### 方法二：使用版本管理器（推荐）
```bash
# 安装 nvm (Node Version Manager)
# macOS/Linux
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# Windows (使用 nvm-windows)
# 下载并安装：https://github.com/coreybutler/nvm-windows

# 安装和使用 Node.js
nvm install 20.10.0
nvm use 20.10.0
nvm alias default 20.10.0
```

### 2. 安装 Git
```bash
# macOS (使用 Homebrew)
brew install git

# Ubuntu/Debian
sudo apt update && sudo apt install git

# Windows
# 下载并安装：https://git-scm.com/download/win

# 配置 Git
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"
```

### 3. 安装代码编辑器

#### VS Code（推荐）
```bash
# 下载并安装：https://code.visualstudio.com/

# 必需扩展
code --install-extension ms-vscode.vscode-typescript-next
code --install-extension esbenp.prettier-vscode
code --install-extension ms-vscode.vscode-eslint
code --install-extension bradlc.vscode-tailwindcss

# 推荐扩展
code --install-extension yzhang.markdown-all-in-one
code --install-extension streetsidesoftware.code-spell-checker
code --install-extension ms-vscode.vscode-json
code --install-extension formulahendry.auto-rename-tag
```

## 项目安装

### 1. 克隆项目
```bash
# 克隆仓库
git clone https://github.com/your-org/tucsenberg-web-1.0.git
cd tucsenberg-web-1.0

# 或者使用 SSH
<NAME_EMAIL>:your-org/tucsenberg-web-1.0.git
cd tucsenberg-web-1.0
```

### 2. 安装依赖
```bash
# 使用 npm（推荐）
npm install

# 或使用 yarn
yarn install

# 或使用 pnpm
pnpm install
```

### 3. 验证依赖安装
```bash
# 检查关键依赖
npm list next react typescript

# 应该看到类似输出：
# ├── next@15.3.3
# ├── react@19.0.0
# └── typescript@5.x.x
```

## 开发工具配置

### 1. VS Code 配置

#### 工作区设置 (.vscode/settings.json)
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true,
    "source.organizeImports": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "files.associations": {
    "*.mdx": "mdx"
  }
}
```

#### 推荐的扩展配置 (.vscode/extensions.json)
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "yzhang.markdown-all-in-one",
    "streetsidesoftware.code-spell-checker"
  ]
}
```

### 2. Git 配置

#### Git Hooks (Husky)
```bash
# Husky 已在项目中配置，安装依赖后自动启用
# 提交前会自动运行：
# - ESLint 检查
# - Prettier 格式化
# - TypeScript 类型检查
# - 测试（如果有）
```

#### Git 忽略文件检查
```bash
# 确保 .gitignore 包含必要的忽略规则
cat .gitignore | grep -E "(node_modules|\.next|\.env\.local)"
```

## 环境变量配置

### 1. 创建环境变量文件
```bash
# 复制环境变量模板
cp .env.example .env.local

# 或手动创建
touch .env.local
```

### 2. 基础环境变量
```bash
# .env.local
# 应用基础配置
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Google API 配置（用于翻译）
GOOGLE_API_KEY=your_google_api_key_here

# 分析工具配置（可选）
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# 其他分析工具（可选）
NEXT_PUBLIC_GA_ID=your_google_analytics_id
NEXT_PUBLIC_HOTJAR_ID=your_hotjar_id
```

### 3. 获取 Google API Key
```bash
# 1. 访问 Google AI Studio
open https://aistudio.google.com/

# 2. 创建新项目或选择现有项目
# 3. 启用 Gemini API
# 4. 创建 API 密钥
# 5. 复制密钥到 .env.local 文件
```

### 4. 验证环境变量
```bash
# 运行环境变量验证脚本
npm run config:check

# 或手动检查
node -e "console.log(process.env.NEXT_PUBLIC_APP_URL)"
```

## 验证安装

### 1. 启动开发服务器
```bash
# 启动开发服务器
npm run dev

# 应该看到类似输出：
# ▲ Next.js 15.3.3
# - Local:        http://localhost:3000
# - Environments: .env.local
```

### 2. 运行测试套件
```bash
# 运行所有测试
npm run test

# 运行类型检查
npm run typecheck

# 运行代码检查
npm run lint

# 运行完整检查
npm run check:all
```

### 3. 验证构建
```bash
# 构建生产版本
npm run build

# 应该成功完成，无错误输出
```

### 4. 验证翻译功能
```bash
# 检查翻译配置
npm run i18n:verify

# 查看翻译状态
npm run i18n:status
```

## 常见问题

### Q1: Node.js 版本不兼容
```bash
# 错误：Node.js version 16.x is not supported
# 解决：升级到 Node.js 18+
nvm install 20.10.0
nvm use 20.10.0
```

### Q2: 依赖安装失败
```bash
# 清理缓存并重新安装
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### Q3: TypeScript 错误
```bash
# 重新生成类型定义
rm -rf .next
npm run typecheck
```

### Q4: 端口被占用
```bash
# 使用不同端口
npm run dev -- -p 3001

# 或杀死占用进程
lsof -ti:3000 | xargs kill -9
```

### Q5: Git Hooks 不工作
```bash
# 重新安装 Husky
npm run prepare
```

### Q6: 环境变量不生效
```bash
# 检查文件名和位置
ls -la .env*

# 重启开发服务器
npm run dev
```

## 开发工作流

### 1. 日常开发流程
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 安装新依赖（如有）
npm install

# 3. 启动开发服务器
npm run dev

# 4. 开发功能...

# 5. 运行检查
npm run check:all

# 6. 提交代码
git add .
git commit -m "feat: 添加新功能"
git push origin feature-branch
```

### 2. 分支管理
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 开发完成后合并
git checkout main
git pull origin main
git merge feature/new-feature
git push origin main
```

### 3. 代码质量检查
```bash
# 格式化代码
npm run format:safe

# 修复 ESLint 问题
npm run lint:fix

# 运行测试
npm run test

# 检查类型
npm run typecheck
```

### 4. 调试技巧
```bash
# 启用详细日志
DEBUG=* npm run dev

# 分析构建包大小
npm run build -- --analyze

# 性能分析
npm run dev -- --turbo
```

## 相关文档

### 下一步阅读
- [项目架构文档](./项目架构文档.md) - 了解项目整体架构
- [文案编辑流程](./文案编辑流程.md) - 了解内容编辑流程
- [翻译工作流程](./翻译工作流程.md) - 了解翻译工作流程

### 深入了解
- [API集成文档](./API集成文档.md) - 了解外部服务集成
- [部署运维指南](./部署运维指南.md) - 了解部署流程

---

**文档维护**：本文档由开发团队维护，如有问题请联系技术负责人。
**最后更新**：2024年1月
