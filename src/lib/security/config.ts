/**
 * 安全配置模块
 * 
 * 提供统一的安全策略配置，包括 CSP、CORS 和其他安全头部
 */

import { env } from '@/env';

// 内容安全策略配置
export const CSP_CONFIG = {
  // 开发环境配置 - 更宽松的策略以支持开发工具
  development: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-eval'", // Turbopack/Webpack HMR 需要
      "'unsafe-inline'", // 开发环境内联脚本
      'localhost:*',
      '127.0.0.1:*',
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Tailwind CSS 和开发工具需要
      'fonts.googleapis.com',
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'localhost:*',
      '127.0.0.1:*',
    ],
    'font-src': [
      "'self'",
      'data:',
      'fonts.gstatic.com',
    ],
    'connect-src': [
      "'self'",
      'localhost:*',
      '127.0.0.1:*',
      'ws://localhost:*',
      'wss://localhost:*',
    ],
    'media-src': ["'self'", 'data:', 'blob:'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
  },

  // 生产环境配置 - 严格的安全策略
  production: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-eval'", // Next.js 运行时需要
      // 分析工具域名
      ...(env.NEXT_PUBLIC_POSTHOG_HOST ? [new URL(env.NEXT_PUBLIC_POSTHOG_HOST).hostname] : []),
      ...(env.NEXT_PUBLIC_GA_MEASUREMENT_ID ? ['www.googletagmanager.com', 'www.google-analytics.com'] : []),
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Tailwind CSS 需要
      'fonts.googleapis.com',
    ],
    'img-src': [
      "'self'",
      'data:',
      'https:',
      // 分析工具图片域名
      ...(env.NEXT_PUBLIC_GA_MEASUREMENT_ID ? ['www.google-analytics.com'] : []),
    ],
    'font-src': [
      "'self'",
      'data:',
      'fonts.gstatic.com',
    ],
    'connect-src': [
      "'self'",
      // API 端点
      env.NEXT_PUBLIC_APP_URL,
      // 分析工具连接
      ...(env.NEXT_PUBLIC_POSTHOG_HOST ? [env.NEXT_PUBLIC_POSTHOG_HOST] : []),
      ...(env.NEXT_PUBLIC_GA_MEASUREMENT_ID ? ['www.google-analytics.com'] : []),
      ...(env.NEXT_PUBLIC_SENTRY_DSN ? [new URL(env.NEXT_PUBLIC_SENTRY_DSN).origin] : []),
    ],
    'media-src': ["'self'", 'data:'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'upgrade-insecure-requests': [],
  },
} as const;

// 安全头部配置
export const SECURITY_HEADERS = {
  // 防止点击劫持
  'X-Frame-Options': 'DENY',
  
  // 防止 MIME 类型嗅探
  'X-Content-Type-Options': 'nosniff',
  
  // 控制引用信息
  'Referrer-Policy': 'origin-when-cross-origin',
  
  // 权限策略 - 禁用不需要的浏览器功能
  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
  ].join(', '),
} as const;

// HSTS 配置（仅生产环境）
export const HSTS_CONFIG = {
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
} as const;

// CORS 配置
export const CORS_CONFIG = {
  // 允许的源
  allowedOrigins: [
    env.NEXT_PUBLIC_APP_URL,
    // 开发环境
    ...(process.env.NODE_ENV === 'development' ? [
      'http://localhost:3000',
      'http://127.0.0.1:3000',
    ] : []),
  ],
  
  // 允许的方法
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  
  // 允许的头部
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin',
  ],
  
  // 是否允许凭证
  credentials: true,
  
  // 预检请求缓存时间
  maxAge: 86400, // 24 小时
} as const;

/**
 * 生成 CSP 字符串
 */
export function generateCSP(): string {
  const isProduction = process.env.NODE_ENV === 'production';
  const config = isProduction ? CSP_CONFIG.production : CSP_CONFIG.development;
  
  return Object.entries(config)
    .map(([directive, sources]) => {
      if (sources.length === 0) {
        return directive.replace('-src', '');
      }
      return `${directive} ${sources.join(' ')}`;
    })
    .join('; ');
}

/**
 * 获取完整的安全头部配置
 */
export function getSecurityHeaders() {
  const headers = {
    ...SECURITY_HEADERS,
    'Content-Security-Policy': generateCSP(),
  };

  // 生产环境添加 HSTS
  if (process.env.NODE_ENV === 'production') {
    Object.assign(headers, HSTS_CONFIG);
  }

  return headers;
}

/**
 * 验证请求源是否被允许
 */
export function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return true; // 同源请求
  
  return CORS_CONFIG.allowedOrigins.some(allowedOrigin => {
    if (allowedOrigin === origin) return true;
    
    // 支持子域名匹配
    try {
      const allowedUrl = new URL(allowedOrigin);
      const originUrl = new URL(origin);
      
      return allowedUrl.hostname === originUrl.hostname ||
             originUrl.hostname.endsWith(`.${allowedUrl.hostname}`);
    } catch {
      return false;
    }
  });
}

/**
 * 安全配置验证
 */
export function validateSecurityConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需的环境变量
  if (!env.NEXT_PUBLIC_APP_URL) {
    errors.push('NEXT_PUBLIC_APP_URL 环境变量未设置');
  }

  // 检查生产环境配置
  if (process.env.NODE_ENV === 'production') {
    if (!env.NEXT_PUBLIC_APP_URL.startsWith('https://')) {
      warnings.push('生产环境建议使用 HTTPS');
    }
    
    if (!env.ARCJET_KEY) {
      warnings.push('建议配置 ARCJET_KEY 以启用高级安全防护');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}
