import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { type Locale } from '@/lib/i18n/config';
import { LocalizedNavigationButton } from '@/components/interactive';
import {
  Calendar,
  Clock,
  User,
  ArrowRight,
  Tag,
  TrendingUp,
} from 'lucide-react';

interface BlogPageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function BlogPage({ params }: BlogPageProps) {
  const { locale } = await params;

  const content = {
    en: {
      title: 'Latest News & Insights',
      subtitle:
        'Stay updated with the latest trends in modern frontend development',
      readMore: 'Read More',
      readTime: 'min read',
      featured: 'Featured',
      latest: 'Latest Posts',
      posts: [
        {
          title: 'Getting Started with Next.js 15 and React 19',
          description:
            'Learn how to build modern web applications with the latest versions of Next.js and React, including new features and best practices.',
          author: 'Tucsenberg Team',
          date: '2024-01-15',
          readTime: 8,
          tags: ['Next.js', 'React', 'Tutorial'],
          featured: true,
        },
        {
          title: 'Building Accessible UI Components with shadcn/ui',
          description:
            'A comprehensive guide to creating accessible and beautiful user interfaces using shadcn/ui and Radix UI primitives.',
          author: 'UI Team',
          date: '2024-01-12',
          readTime: 12,
          tags: ['UI/UX', 'Accessibility', 'Components'],
          featured: false,
        },
        {
          title: 'TypeScript Best Practices for Large Applications',
          description:
            'Discover advanced TypeScript patterns and practices that will help you build maintainable and scalable applications.',
          author: 'Dev Team',
          date: '2024-01-10',
          readTime: 15,
          tags: ['TypeScript', 'Best Practices', 'Architecture'],
          featured: false,
        },
        {
          title: 'State Management with Zustand vs Redux',
          description:
            'Compare different state management solutions and learn when to use Zustand for your React applications.',
          author: 'Frontend Team',
          date: '2024-01-08',
          readTime: 10,
          tags: ['State Management', 'Zustand', 'Redux'],
          featured: false,
        },
      ],
    },
    zh: {
      title: '最新资讯与见解',
      subtitle: '了解现代化前端开发的最新趋势和技术动态',
      readMore: '阅读更多',
      readTime: '分钟阅读',
      featured: '精选',
      latest: '最新文章',
      posts: [
        {
          title: 'Next.js 15 和 React 19 入门指南',
          description:
            '学习如何使用最新版本的 Next.js 和 React 构建现代化 Web 应用程序，包括新特性和最佳实践。',
          author: 'Tucsenberg 团队',
          date: '2024-01-15',
          readTime: 8,
          tags: ['Next.js', 'React', '教程'],
          featured: true,
        },
        {
          title: '使用 shadcn/ui 构建无障碍 UI 组件',
          description:
            '使用 shadcn/ui 和 Radix UI 原语创建无障碍且美观的用户界面的综合指南。',
          author: 'UI 团队',
          date: '2024-01-12',
          readTime: 12,
          tags: ['UI/UX', '无障碍', '组件'],
          featured: false,
        },
        {
          title: '大型应用的 TypeScript 最佳实践',
          description:
            '探索高级 TypeScript 模式和实践，帮助您构建可维护和可扩展的应用程序。',
          author: '开发团队',
          date: '2024-01-10',
          readTime: 15,
          tags: ['TypeScript', '最佳实践', '架构'],
          featured: false,
        },
        {
          title: 'Zustand vs Redux 状态管理对比',
          description:
            '比较不同的状态管理解决方案，了解何时在 React 应用程序中使用 Zustand。',
          author: '前端团队',
          date: '2024-01-08',
          readTime: 10,
          tags: ['状态管理', 'Zustand', 'Redux'],
          featured: false,
        },
      ],
    },
  };

  const currentContent = content[locale];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return locale === 'zh'
      ? date.toLocaleDateString('zh-CN')
      : date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
  };

  const featuredPost = currentContent.posts.find((post) => post.featured);
  const regularPosts = currentContent.posts.filter((post) => !post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            {currentContent.title}
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-3xl mx-auto">
            {currentContent.subtitle}
          </p>
        </div>

        {/* Featured Post */}
        {featuredPost && (
          <div className="mb-16">
            <div className="flex items-center gap-2 mb-6">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <h2 className="text-2xl font-bold">{currentContent.featured}</h2>
            </div>

            <Card className="overflow-hidden hover:shadow-lg transition-shadow">
              <div className="md:flex">
                <div className="md:w-1/3 bg-gradient-to-br from-blue-500 to-purple-600 p-8 flex items-center justify-center">
                  <div className="text-white text-center">
                    <TrendingUp className="h-16 w-16 mx-auto mb-4" />
                    <div className="text-sm font-medium">
                      {currentContent.featured}
                    </div>
                  </div>
                </div>
                <div className="md:w-2/3 p-8">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="text-2xl mb-2">
                      {featuredPost.title}
                    </CardTitle>
                    <CardDescription className="text-base">
                      {featuredPost.description}
                    </CardDescription>
                  </CardHeader>

                  <CardContent className="p-0">
                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <User className="h-4 w-4" />
                        {featuredPost.author}
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {formatDate(featuredPost.date)}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {featuredPost.readTime} {currentContent.readTime}
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-2 mb-6">
                      {featuredPost.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-md text-xs"
                        >
                          <Tag className="h-3 w-3" />
                          {tag}
                        </span>
                      ))}
                    </div>

                    <LocalizedNavigationButton
                      href={`/blog/${featuredPost.title
                        .toLowerCase()
                        .replace(/\s+/g, '-')
                        .replace(/[^\w-]/g, '')}`}
                      className="bg-primary text-primary-foreground shadow-xs hover:bg-primary/90 h-9 px-4 py-2"
                      showIcon={false}
                    >
                      {currentContent.readMore}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </LocalizedNavigationButton>
                  </CardContent>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Latest Posts */}
        <div>
          <h2 className="text-2xl font-bold mb-8">{currentContent.latest}</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {regularPosts.map((post, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg line-clamp-2">
                    {post.title}
                  </CardTitle>
                  <CardDescription className="line-clamp-3">
                    {post.description}
                  </CardDescription>
                </CardHeader>

                <CardContent>
                  <div className="flex flex-wrap items-center gap-3 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <User className="h-3 w-3" />
                      {post.author}
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(post.date)}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {post.readTime} {currentContent.readTime}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {post.tags.slice(0, 2).map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-flex items-center gap-1 px-2 py-1 bg-slate-100 dark:bg-slate-800 text-slate-700 dark:text-slate-300 rounded text-xs"
                      >
                        <Tag className="h-2 w-2" />
                        {tag}
                      </span>
                    ))}
                    {post.tags.length > 2 && (
                      <span className="text-xs text-muted-foreground">
                        +{post.tags.length - 2}
                      </span>
                    )}
                  </div>

                  <LocalizedNavigationButton
                    href={`/blog/${post.title
                      .toLowerCase()
                      .replace(/\s+/g, '-')
                      .replace(/[^\w-]/g, '')}`}
                    variant="outline"
                    className="w-full h-8 px-3"
                    showIcon={false}
                  >
                    {currentContent.readMore}
                    <ArrowRight className="ml-2 h-3 w-3" />
                  </LocalizedNavigationButton>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
