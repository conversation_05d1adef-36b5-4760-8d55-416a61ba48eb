# 文案编辑与翻译工作流程文档

## 📋 文档概述

本文档库包含了项目中文案编辑和翻译工作的完整流程指南，涵盖从内容创建到最终发布的所有环节。

## 📚 文档结构

### 核心流程文档

| 文档 | 适用人员 | 主要内容 |
|------|----------|----------|
| [文案编辑流程](./文案编辑流程.md) | 内容编辑、产品经理 | 内容创建、编辑规范、质量控制 |
| [翻译工作流程](./翻译工作流程.md) | 开发者、翻译审核员 | AI翻译、质量审核、自动化流程 |
| [国际化配置指南](./国际化配置指南.md) | 开发者、DevOps工程师 | 技术配置、环境设置、维护指南 |

### 技术架构文档

| 文档 | 适用人员 | 主要内容 |
|------|----------|----------|
| [项目架构文档](./项目架构文档.md) | 全体开发者 | 技术架构、系统设计、技术选型 |
| [开发环境搭建指南](./开发环境搭建指南.md) | 新开发者 | 环境配置、工具安装、快速上手 |
| [API集成文档](./API集成文档.md) | 后端开发者 | 外部服务集成、API设计、安全配置 |
| [部署运维指南](./部署运维指南.md) | DevOps工程师 | 部署流程、监控告警、故障处理 |

## 🔄 完整工作流程

```mermaid
graph TD
    A[需求收集] --> B[内容创建]
    B --> C[文案编辑]
    C --> D[格式检查]
    D --> E[内容审核]
    E --> F[提交到Git]
    F --> G[自动翻译]
    G --> H[翻译审核]
    H --> I[质量检查]
    I --> J[发布上线]
    
    K[配置管理] --> G
    L[环境维护] --> G
```

## 👥 角色与职责

### 内容团队
- **内容编辑**：负责英文原始内容的创建和编辑
- **内容审核员**：负责内容质量和准确性审核
- **品牌经理**：确保内容符合品牌调性和标准

### 技术团队
- **前端开发者**：维护界面文案和国际化配置
- **翻译审核员**：审核AI翻译质量和准确性
- **DevOps工程师**：维护自动化流程和环境配置

## 🛠️ 技术栈

### 核心技术
- **框架**：Next.js 15 + React 19
- **国际化**：App Router + Middleware
- **内容格式**：MDX + TypeScript
- **翻译引擎**：Google Gemini 2.0 Pro
- **管理工具**：Lingo.dev CLI

### 自动化工具
- **CI/CD**：GitHub Actions
- **代码质量**：ESLint + Prettier
- **测试框架**：Vitest + Testing Library
- **版本控制**：Git + Husky

## 📁 项目结构

```
project/
├── docs/                   # 📖 项目文档
│   ├── README.md          # 总览文档
│   ├── 文案编辑流程.md      # 内容编辑指南
│   ├── 翻译工作流程.md      # 翻译管理指南
│   ├── 国际化配置指南.md    # 技术配置指南
│   ├── 项目架构文档.md      # 技术架构设计
│   ├── 开发环境搭建指南.md  # 环境配置指南
│   ├── API集成文档.md      # API集成说明
│   └── 部署运维指南.md      # 部署运维手册
├── content/               # 📝 内容文件
│   ├── en/               # 英文内容（源语言）
│   └── zh/               # 中文内容（翻译）
├── src/lib/i18n/         # 🌍 国际化配置
│   ├── config.ts         # 语言配置
│   ├── translations.ts   # 界面文案
│   └── use-translations.ts # 翻译Hook
├── i18n.json             # ⚙️ Lingo.dev配置
└── .github/workflows/    # 🤖 自动化流程
    └── i18n.yml          # 翻译工作流
```

## 🚀 快速开始

### 新手入门
1. **新开发者**：阅读 [开发环境搭建指南](./开发环境搭建指南.md) → [项目架构文档](./项目架构文档.md)
2. **内容编辑人员**：阅读 [文案编辑流程](./文案编辑流程.md)
3. **翻译管理员**：阅读 [翻译工作流程](./翻译工作流程.md)
4. **DevOps工程师**：阅读 [部署运维指南](./部署运维指南.md)
5. **后端开发者**：阅读 [API集成文档](./API集成文档.md)

### 常用命令
```bash
# 开发环境
npm run dev              # 启动开发服务器
npm run format:check     # 检查格式
npm run lint            # 代码检查

# 翻译相关
npm run i18n:status     # 查看翻译状态
npm run i18n:translate  # 执行翻译
npm run i18n:verify     # 验证配置

# 质量检查
npm run test            # 运行测试
npm run build           # 构建项目
npm run check:all       # 全面检查
```

## 📊 质量标准

### 内容质量
- ✅ 语法正确，表达清晰
- ✅ 符合品牌调性和风格
- ✅ 技术术语使用准确
- ✅ 格式规范，结构清晰

### 翻译质量
- ✅ 准确传达原文意思
- ✅ 中文表达自然流畅
- ✅ 保持技术术语原文
- ✅ 格式完整无损失

### 技术质量
- ✅ 代码规范，无语法错误
- ✅ 类型安全，无类型错误
- ✅ 测试覆盖，功能正常
- ✅ 性能优化，加载快速

## 🔧 工具配置

### 开发环境
```bash
# 必需环境变量
NEXT_PUBLIC_APP_URL=http://localhost:3000
GOOGLE_API_KEY=your_google_api_key

# 推荐VS Code扩展
- Markdown All in One
- Prettier - Code formatter
- ESLint
- Code Spell Checker
```

### 生产环境
```bash
# 生产环境变量
NEXT_PUBLIC_APP_URL=https://your-domain.com
GOOGLE_API_KEY=your_production_api_key

# GitHub Secrets
GOOGLE_API_KEY=your_github_secret_key
```

## 📈 监控与维护

### 定期检查
- **每日**：检查自动翻译状态
- **每周**：审核翻译质量，更新内容
- **每月**：检查API使用量，优化配置

### 性能监控
- **翻译准确性**：人工抽查翻译质量
- **系统性能**：监控构建时间和加载速度
- **API使用量**：跟踪Google API调用次数

## 🆘 紧急联系

### 技术问题
- **开发问题**：联系前端开发团队
- **翻译问题**：联系翻译管理员
- **部署问题**：联系DevOps团队

### 内容问题
- **编辑问题**：联系内容编辑团队
- **品牌问题**：联系品牌管理团队
- **法务问题**：联系法务团队

## 📝 更新日志

### 2024年1月
- ✅ 创建完整的流程文档体系
- ✅ 建立标准化的工作流程
- ✅ 配置自动化翻译系统
- ✅ 实现质量控制机制

---

**文档维护**：本文档由项目团队共同维护，如有问题请联系相关负责人。
**最后更新**：2024年1月
