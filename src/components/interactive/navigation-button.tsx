'use client';

import * as React from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ExternalLink, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  type Locale,
  getLocaleFromPathname,
  defaultLocale,
} from '@/lib/i18n/config';

/**
 * 专门的导航交互组件
 *
 * 这些组件专门用于处理页面导航，包括内部路由跳转和外部链接。
 * 完全封装导航逻辑，符合 React 19 严格的组件边界规则。
 *
 * @example
 * // 内部导航按钮
 * <NavigationButton href="/products" variant="default">
 *   查看产品
 * </NavigationButton>
 *
 * // 外部链接按钮
 * <ExternalLinkButton href="https://example.com" newTab>
 *   访问外部网站
 * </ExternalLinkButton>
 *
 * // 带国际化的内部导航
 * <LocalizedNavigationButton href="/products" preserveLocale>
 *   查看产品
 * </LocalizedNavigationButton>
 */

// 安全的 Button props，排除所有函数类型
type SafeButtonProps = Omit<
  React.ComponentProps<typeof Button>,
  | 'onClick'
  | 'onMouseDown'
  | 'onMouseUp'
  | 'onKeyDown'
  | 'onKeyUp'
  | 'onFocus'
  | 'onBlur'
  | 'onSubmit'
>;

/**
 * 基础导航按钮组件
 */
interface NavigationButtonProps extends SafeButtonProps {
  /**
   * 导航目标 URL
   */
  href: string;

  /**
   * 是否为外部链接
   */
  external?: boolean;

  /**
   * 是否在新标签页打开
   */
  newTab?: boolean;

  /**
   * 是否显示外部链接图标
   */
  showExternalIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';

  /**
   * 是否使用客户端路由（Next.js router.push）
   */
  useClientRouting?: boolean;
}

export function NavigationButton({
  href,
  external = false,
  newTab = false,
  showExternalIcon = true,
  iconPosition = 'right',
  useClientRouting = true,
  children,
  className,
  ...props
}: NavigationButtonProps) {
  const router = useRouter();

  // 自动检测是否为外部链接
  const isExternal =
    external ||
    href.startsWith('http') ||
    href.startsWith('mailto:') ||
    href.startsWith('tel:');

  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (isExternal) {
        // 外部链接处理
        if (newTab) {
          window.open(href, '_blank', 'noopener,noreferrer');
        } else {
          window.location.href = href;
        }
      } else {
        // 内部链接处理
        if (useClientRouting) {
          router.push(href);
        } else {
          window.location.href = href;
        }
      }
    },
    [href, isExternal, newTab, useClientRouting, router]
  );

  const icon =
    isExternal && showExternalIcon ? (
      <ExternalLink className="h-4 w-4" />
    ) : (
      <ArrowRight className="h-4 w-4" />
    );

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={
        isExternal
          ? `Open ${href} in ${newTab ? 'new tab' : 'current tab'}`
          : `Navigate to ${href}`
      }
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 外部链接按钮组件
 */
interface ExternalLinkButtonProps extends SafeButtonProps {
  /**
   * 外部链接 URL
   */
  href: string;

  /**
   * 是否在新标签页打开（默认为 true）
   */
  newTab?: boolean;

  /**
   * 是否显示外部链接图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function ExternalLinkButton({
  href,
  newTab = true,
  showIcon = true,
  iconPosition = 'right',
  children,
  className,
  ...props
}: ExternalLinkButtonProps) {
  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      if (newTab) {
        window.open(href, '_blank', 'noopener,noreferrer');
      } else {
        window.location.href = href;
      }
    },
    [href, newTab]
  );

  const icon = showIcon ? <ExternalLink className="h-4 w-4" /> : null;

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={`Open ${href} in ${newTab ? 'new tab' : 'current tab'}`}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 带国际化支持的导航按钮组件
 */
interface LocalizedNavigationButtonProps extends SafeButtonProps {
  /**
   * 导航目标 URL（不包含语言前缀）
   */
  href: string;

  /**
   * 是否保持当前语言设置
   */
  preserveLocale?: boolean;

  /**
   * 指定目标语言（如果不保持当前语言）
   */
  targetLocale?: Locale;

  /**
   * 是否显示导航图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';

  /**
   * 是否使用客户端路由
   */
  useClientRouting?: boolean;
}

export function LocalizedNavigationButton({
  href,
  preserveLocale = true,
  targetLocale,
  showIcon = true,
  iconPosition = 'right',
  useClientRouting = true,
  children,
  className,
  ...props
}: LocalizedNavigationButtonProps) {
  const router = useRouter();
  const pathname = usePathname();

  const handleClick = React.useCallback(
    (e: React.MouseEvent<HTMLButtonElement>) => {
      e.preventDefault();

      let targetUrl = href;

      if (preserveLocale) {
        // 保持当前语言设置
        const currentLocale = getLocaleFromPathname(pathname);
        if (currentLocale !== defaultLocale) {
          targetUrl = `/${currentLocale}${href.startsWith('/') ? href : '/' + href}`;
        }
      } else if (targetLocale && targetLocale !== defaultLocale) {
        // 使用指定的目标语言
        targetUrl = `/${targetLocale}${href.startsWith('/') ? href : '/' + href}`;
      }

      if (useClientRouting) {
        router.push(targetUrl);
      } else {
        window.location.href = targetUrl;
      }
    },
    [href, preserveLocale, targetLocale, pathname, useClientRouting, router]
  );

  const icon = showIcon ? <ArrowRight className="h-4 w-4" /> : null;

  return (
    <Button
      onClick={handleClick}
      className={cn('inline-flex items-center gap-2', className)}
      aria-label={`Navigate to ${href}`}
      {...props}
    >
      {iconPosition === 'left' && icon}
      {children}
      {iconPosition === 'right' && icon}
    </Button>
  );
}

/**
 * 简单的链接按钮组件
 * 用于基本的链接跳转，自动检测内部/外部链接
 */
interface LinkButtonProps extends SafeButtonProps {
  /**
   * 链接 URL
   */
  href: string;

  /**
   * 是否显示图标
   */
  showIcon?: boolean;

  /**
   * 图标位置
   */
  iconPosition?: 'left' | 'right';
}

export function LinkButton({
  href,
  showIcon = true,
  iconPosition = 'right',
  children,
  className,
  ...props
}: LinkButtonProps) {
  const isHttpExternal = href.startsWith('http');
  const isProtocolLink = href.startsWith('mailto:') || href.startsWith('tel:');

  if (isHttpExternal) {
    return (
      <ExternalLinkButton
        href={href}
        showIcon={showIcon}
        iconPosition={iconPosition}
        className={className}
        {...props}
      >
        {children}
      </ExternalLinkButton>
    );
  }

  if (isProtocolLink) {
    return (
      <ExternalLinkButton
        href={href}
        newTab={false}
        showIcon={false}
        iconPosition={iconPosition}
        className={className}
        {...props}
      >
        {children}
      </ExternalLinkButton>
    );
  }

  return (
    <NavigationButton
      href={href}
      showExternalIcon={false}
      iconPosition={iconPosition}
      className={className}
      {...props}
    >
      {children}
    </NavigationButton>
  );
}
