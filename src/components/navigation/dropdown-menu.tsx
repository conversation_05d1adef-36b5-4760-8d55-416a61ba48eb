'use client';

import * as React from 'react';
import Link from 'next/link';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { type NavigationItem } from '@/types/i18n';
import { type Locale } from '@/lib/i18n/config';

interface DropdownMenuProps {
  item: NavigationItem;
  locale: Locale;
  className?: string;
}

export function DropdownMenu({ item, locale, className }: DropdownMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const timeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false);
    }, 150);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      setIsOpen(!isOpen);
    } else if (event.key === 'Escape') {
      setIsOpen(false);
    }
  };

  const handleChildClick = () => {
    setIsOpen(false);
  };

  React.useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  if (!item.children || item.children.length === 0) {
    return (
      <Link
        href={`/${locale === 'en' ? '' : locale + '/'}${item.href.slice(1)}`}
        className={cn(
          'transition-colors px-3 py-2 rounded-md text-sm font-medium',
          className
        )}
      >
        {item.label[locale]}
      </Link>
    );
  }

  return (
    <div
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <button
        className={cn(
          'flex items-center gap-1 transition-colors px-3 py-2 rounded-md text-sm font-medium',
          className
        )}
        onKeyDown={handleKeyDown}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        {item.label[locale]}
        <ChevronDown
          className={cn(
            'h-4 w-4 transition-transform duration-200',
            isOpen && 'rotate-180'
          )}
        />
      </button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-slate-200 dark:border-slate-700 py-2 z-50 animate-in fade-in-0 zoom-in-95 duration-200">
          {item.children.map((child, index) => (
            <Link
              key={index}
              href={`/${locale === 'en' ? '' : locale + '/'}${child.href.slice(1)}`}
              className="block px-4 py-3 text-sm text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
              onClick={handleChildClick}
            >
              <div className="font-medium">{child.label[locale]}</div>
              {child.description && (
                <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {child.description[locale]}
                </div>
              )}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}

// Mobile dropdown menu component
interface MobileDropdownMenuProps {
  item: NavigationItem;
  locale: Locale;
  onItemClick?: () => void;
}

export function MobileDropdownMenu({
  item,
  locale,
  onItemClick,
}: MobileDropdownMenuProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleItemClick = () => {
    setIsOpen(false);
    onItemClick?.();
  };

  if (!item.children || item.children.length === 0) {
    return (
      <Link
        href={`/${locale === 'en' ? '' : locale + '/'}${item.href.slice(1)}`}
        className="block px-4 py-3 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
        onClick={onItemClick}
      >
        {item.label[locale]}
      </Link>
    );
  }

  return (
    <div className="border-b border-slate-200 dark:border-slate-700">
      <button
        className="flex items-center justify-between w-full px-4 py-3 text-base font-medium text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
        onClick={handleToggle}
        aria-expanded={isOpen}
      >
        {item.label[locale]}
        <ChevronDown
          className={cn(
            'h-4 w-4 transition-transform duration-200',
            isOpen && 'rotate-180'
          )}
        />
      </button>

      {isOpen && (
        <div className="bg-slate-50 dark:bg-slate-800 animate-in slide-in-from-top-2 duration-200">
          {item.children.map((child, index) => (
            <Link
              key={index}
              href={`/${locale === 'en' ? '' : locale + '/'}${child.href.slice(1)}`}
              className="block px-8 py-3 text-sm text-slate-600 dark:text-slate-300 hover:text-slate-900 dark:hover:text-slate-100 hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
              onClick={handleItemClick}
            >
              <div className="font-medium">{child.label[locale]}</div>
              {child.description && (
                <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {child.description[locale]}
                </div>
              )}
            </Link>
          ))}
        </div>
      )}
    </div>
  );
}
