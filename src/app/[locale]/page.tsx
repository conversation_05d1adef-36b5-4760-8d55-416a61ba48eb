import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  CheckCircle,
  Code,
  Database,
  Palette,
  Shield,
  Zap,
  Type,
} from 'lucide-react';
import { type Locale } from '@/lib/i18n/config';
import { ThemeToggleButton } from '@/components/navigation/theme-toggle-button';
import { LocalizedNavigationButton } from '@/components/interactive';
import { getServerTranslations } from '@/lib/i18n/use-translations';

interface HomeProps {
  params: Promise<{ locale: Locale }>;
}

export default async function Home({ params }: HomeProps) {
  // 在服务器组件中正确处理 Promise params
  const { locale } = await params;

  // 使用新的翻译系统
  const t = getServerTranslations(locale);

  // 定义特性数据（图标和翻译键的映射）
  const features = [
    {
      icon: <Code className="h-6 w-6" />,
      titleKey: 'home.features.nextjs.title',
      descriptionKey: 'home.features.nextjs.description',
    },
    {
      icon: <Palette className="h-6 w-6" />,
      titleKey: 'home.features.ui.title',
      descriptionKey: 'home.features.ui.description',
    },
    {
      icon: <Database className="h-6 w-6" />,
      titleKey: 'home.features.state.title',
      descriptionKey: 'home.features.state.description',
    },
    {
      icon: <Shield className="h-6 w-6" />,
      titleKey: 'home.features.typescript.title',
      descriptionKey: 'home.features.typescript.description',
    },
    {
      icon: <Zap className="h-6 w-6" />,
      titleKey: 'home.features.testing.title',
      descriptionKey: 'home.features.testing.description',
    },
    {
      icon: <CheckCircle className="h-6 w-6" />,
      titleKey: 'home.features.ai.title',
      descriptionKey: 'home.features.ai.description',
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent mb-6">
            {t.home.title}
          </h1>
          <p className="text-xl text-slate-600 dark:text-slate-300 mb-8 max-w-2xl mx-auto">
            {t.home.subtitle}
          </p>
          <div className="flex gap-4 justify-center flex-wrap">
            <LocalizedNavigationButton
              href="/products"
              size="lg"
              className="bg-primary hover:bg-primary/90"
            >
              {t.home.getStarted}
            </LocalizedNavigationButton>
            <LocalizedNavigationButton
              href="/font-test"
              size="lg"
              variant="outline"
              className="flex items-center gap-2"
              showIcon={false}
            >
              <Type className="h-4 w-4 mr-2" />
              字体测试 / Font Test
            </LocalizedNavigationButton>
            <ThemeToggleButton toggleText={t.home.toggleTheme} />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-primary/10 dark:bg-primary/20 rounded-lg text-primary">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-lg">
                    {t(feature.titleKey)}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {t(feature.descriptionKey)}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">
                {t.home.configComplete}
              </CardTitle>
              <CardDescription>{t.home.configDescription}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                {t.home.checklist.map((item, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-secondary" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
