import type { NextConfig } from 'next';
import createMDX from '@next/mdx';

const nextConfig: NextConfig = {
  // MDX 页面扩展支持
  pageExtensions: ['js', 'jsx', 'mdx', 'ts', 'tsx'],

  // Turbopack 配置（稳定版）
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // 图片优化配置
  images: {
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },

  // 国际化配置（App Router 使用中间件处理）
  // 注意：App Router 不支持 next.config.ts 中的 i18n 配置
  // 国际化将通过 middleware.ts 实现

  // 编译配置
  typescript: {
    ignoreBuildErrors: false,
  },

  eslint: {
    ignoreDuringBuilds: false,
  },

  // 性能优化
  poweredByHeader: false,
  compress: true,

  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },

  // 安全头部配置
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          // 防止点击劫持攻击
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          // 防止 MIME 类型嗅探
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          // 控制引用信息传递
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          // 强制 HTTPS（生产环境）
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=31536000; includeSubDomains',
          },
          // 权限策略
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
          // 内容安全策略 - 兼容 Turbopack 和 MDX
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline'", // Turbopack 需要 unsafe-eval
              "style-src 'self' 'unsafe-inline'", // Tailwind CSS 需要 unsafe-inline
              "img-src 'self' data: https:", // 支持外部图片和 data URLs
              "font-src 'self' data:",
              "connect-src 'self'",
              "media-src 'self'",
              "object-src 'none'",
              "base-uri 'self'",
              "form-action 'self'",
              "frame-ancestors 'none'",
              "upgrade-insecure-requests"
            ].join('; '),
          },
        ],
      },
    ];
  },
};

// MDX 配置
const withMDX = createMDX({
  // 添加 markdown 插件配置
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

// 导出配置，应用 MDX 插件
export default withMDX(nextConfig);
