#!/usr/bin/env tsx

/**
 * Gemini集成验证脚本
 * 验证Lingo.dev与Gemini API的集成配置
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

interface VerificationResult {
  name: string;
  status: 'pass' | 'warning' | 'fail';
  message: string;
  action?: string;
}

function runCommand(command: string): string {
  try {
    return execSync(command, { encoding: 'utf8', cwd: process.cwd() });
  } catch (error) {
    throw new Error(`Command failed: ${command}\n${error}`);
  }
}

function checkLingoVersion(): VerificationResult {
  try {
    const version = runCommand('npx lingo.dev --version').trim();
    const versionNumber = parseFloat(version.replace('v', ''));

    if (versionNumber >= 0.99) {
      return {
        name: 'Lingo.dev CLI Version',
        status: 'pass',
        message: `✅ CLI版本正确: ${version}`
      };
    } else {
      return {
        name: 'Lingo.dev CLI Version',
        status: 'warning',
        message: `⚠️  CLI版本: ${version} (建议≥v0.101.0支持内置Gemini)`,
        action: '升级到lingo.dev@latest获得更好的Gemini支持'
      };
    }
  } catch (error) {
    return {
      name: 'Lingo.dev CLI Version',
      status: 'fail',
      message: '❌ 无法检测CLI版本',
      action: '安装lingo.dev CLI: npm install -D lingo.dev@latest'
    };
  }
}

function checkI18nConfig(): VerificationResult {
  try {
    const configPath = path.join(process.cwd(), 'i18n.json');
    if (!fs.existsSync(configPath)) {
      return {
        name: 'i18n.json配置',
        status: 'fail',
        message: '❌ i18n.json文件不存在',
        action: '创建i18n.json配置文件'
      };
    }

    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

    if (config.provider?.id === 'google' && config.provider?.model?.includes('gemini')) {
      return {
        name: 'i18n.json Provider',
        status: 'pass',
        message: `✅ Google Gemini provider配置正确 (model: ${config.provider.model})`
      };
    } else {
      return {
        name: 'i18n.json Provider',
        status: 'fail',
        message: '❌ 未配置Google Gemini provider',
        action: '在i18n.json中添加provider配置 (id: "google", model: "gemini-*")'
      };
    }
  } catch (error) {
    return {
      name: 'i18n.json配置',
      status: 'fail',
      message: '❌ 配置文件解析失败',
      action: '检查i18n.json语法'
    };
  }
}

function checkGoogleApiKey(): VerificationResult {
  const apiKey = process.env.GOOGLE_API_KEY;

  if (!apiKey) {
    return {
      name: 'Google API Key',
      status: 'warning',
      message: '⚠️  本地GOOGLE_API_KEY未设置（CI/CD中正常）',
      action: '如需本地测试，请在.env.local中设置GOOGLE_API_KEY'
    };
  }

  if (apiKey.startsWith('AIzaSy')) {
    return {
      name: 'Google API Key',
      status: 'pass',
      message: '✅ Google API Key已正确配置'
    };
  } else {
    return {
      name: 'Google API Key',
      status: 'warning',
      message: '⚠️  API Key格式可能不正确',
      action: '确认API Key是否为有效的Google AI API Key'
    };
  }
}

function checkGitHubWorkflow(): VerificationResult {
  try {
    const workflowPath = path.join(process.cwd(), '.github/workflows/i18n.yml');
    if (!fs.existsSync(workflowPath)) {
      return {
        name: 'GitHub Workflow',
        status: 'fail',
        message: '❌ GitHub Actions工作流文件不存在',
        action: '创建.github/workflows/i18n.yml文件'
      };
    }

    const workflow = fs.readFileSync(workflowPath, 'utf8');
    if (workflow.includes('GOOGLE_API_KEY') && workflow.includes('lingodotdev/lingo.dev@main')) {
      return {
        name: 'GitHub Workflow',
        status: 'pass',
        message: '✅ GitHub Actions工作流配置正确'
      };
    } else {
      return {
        name: 'GitHub Workflow',
        status: 'warning',
        message: '⚠️  工作流配置可能不完整',
        action: '检查workflow中的环境变量和action配置'
      };
    }
  } catch (error) {
    return {
      name: 'GitHub Workflow',
      status: 'fail',
      message: '❌ 工作流文件读取失败',
      action: '检查.github/workflows/i18n.yml文件'
    };
  }
}

function checkLingoStatus(): VerificationResult {
  try {
    const output = runCommand('npx lingo.dev show config');
    const config = JSON.parse(output);

    if (config.provider?.id === 'google' && config.provider?.model?.includes('gemini')) {
      return {
        name: 'Lingo配置验证',
        status: 'pass',
        message: '✅ Lingo配置验证通过'
      };
    } else {
      return {
        name: 'Lingo配置验证',
        status: 'fail',
        message: '❌ Lingo配置验证失败',
        action: '检查i18n.json配置'
      };
    }
  } catch (error) {
    return {
      name: 'Lingo配置验证',
      status: 'warning',
      message: '⚠️  无法验证Lingo配置',
      action: '检查lingo.dev CLI是否正常工作'
    };
  }
}

async function main() {
  // 加载环境变量
  config({ path: '.env.local' });
  config({ path: '.env' });

  console.log('🔍 验证Gemini集成配置...\n');

  const checks = [
    checkLingoVersion(),
    checkI18nConfig(),
    checkGoogleApiKey(),
    checkGitHubWorkflow(),
    checkLingoStatus()
  ];

  const results = {
    pass: 0,
    warning: 0,
    fail: 0
  };

  checks.forEach(check => {
    results[check.status]++;
    console.log(`${check.message}`);
    if (check.action) {
      console.log(`   💡 ${check.action}`);
    }
    console.log();
  });

  console.log('📊 验证结果汇总:');
  console.log(`✅ 通过: ${results.pass}`);
  console.log(`⚠️  警告: ${results.warning}`);
  console.log(`❌ 失败: ${results.fail}`);

  const isReady = results.fail === 0;
  console.log(`\n🚀 集成状态: ${isReady ? '✅ 就绪' : '❌ 需要修复'}`);

  // 保存结果到文件
  const report = {
    timestamp: new Date().toISOString(),
    summary: results,
    details: checks,
    ready: isReady
  };

  fs.writeFileSync('gemini-integration-report.json', JSON.stringify(report, null, 2));
  console.log('\n📄 详细报告已保存到: gemini-integration-report.json');

  process.exit(isReady ? 0 : 1);
}

if (require.main === module) {
  main().catch(console.error);
}
