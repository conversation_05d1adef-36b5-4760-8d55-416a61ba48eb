import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import reactYouMightNotNeedAnEffect from "eslint-plugin-react-you-might-not-need-an-effect";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // 忽略规则
  {
    ignores: [
      "node_modules/**",
      ".next/**",
      ".vercel/**",
      "coverage/**",
      "backups/**",
      "*.config.js",
      "*.config.mjs"
    ]
  },

  // 基础 Next.js 配置
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  // React useEffect 反模式防护配置
  {
    files: ["**/*.{ts,tsx}"],
    plugins: {
      "react-you-might-not-need-an-effect": reactYouMightNotNeedAnEffect
    },
    rules: {
      // 🚨 核心规则：检测不必要的 useEffect
      "react-you-might-not-need-an-effect/you-might-not-need-an-effect": "error",

      // 🔧 相关的 React Hooks 规则
      "react-hooks/rules-of-hooks": "error",
      "react-hooks/exhaustive-deps": "error",

      // 🎯 TypeScript 严格规则
      "@typescript-eslint/no-explicit-any": "error",
      "@typescript-eslint/no-non-null-assertion": "error"
    }
  },

  // 客户端组件特殊配置
  {
    files: ["**/*client*.{ts,tsx}", "**/use-*.{ts,tsx}"],
    rules: {
      // 客户端组件和 hooks 中允许更灵活的 useEffect 使用
      "react-you-might-not-need-an-effect/you-might-not-need-an-effect": "warn"
    }
  },

  // 测试文件配置
  {
    files: ["**/__tests__/**/*.{ts,tsx}", "**/*.test.{ts,tsx}"],
    rules: {
      // 测试文件中放宽规则
      "react-you-might-not-need-an-effect/you-might-not-need-an-effect": "off"
    }
  }
];

export default eslintConfig;
