# Tucsenberg Web 1.0

现代化前端技术方案 - 基于 Next.js 15 和 React 19 的企业级开发框架

## 🎯 项目概述

这是一套现代化前端技术方案，专注于前端开发的最佳实践，特别针对AI辅助开发进行了优化。

### 核心特性

- 🎨 **完整设计系统** - 基于 shadcn/ui 的现代UI组件库
- 🌍 **国际化支持** - 多语言路由和内容管理
- 🔒 **安全防护** - 企业级安全配置
- 📊 **监控分析** - 错误追踪和性能监控
- 🧪 **测试覆盖** - 完整的测试体系
- 🚀 **性能优化** - 现代化构建和优化策略
- 🤖 **AI友好** - 完整的AI反模式防护体系

## 🛠️ 技术栈

### 基础框架

- **Next.js 15** - React 全栈框架
- **React 19** - 用户界面库
- **TypeScript 5.8+** - 类型安全
- **Tailwind CSS 4.1+** - 样式框架
- **shadcn/ui** - 组件库

### 🏗️ 组件架构

- **专门交互组件** - 符合 React 19 严格边界规则
- **静态生成优化** - 移除 force-dynamic 配置
- **类型安全设计** - 完整的 TypeScript 支持
- **无障碍访问** - WCAG 2.1 标准兼容

### 核心依赖

- **Zustand 5.0+** - 状态管理
- **@tanstack/react-query 5.80+** - 数据获取
- **react-hook-form 7.57+** - 表单处理
- **zod 3.25+** - 数据验证
- **framer-motion 12.16+** - 动画库
- **lucide-react 0.511+** - 图标库

### 开发工具

- **Vitest 3.1+** - 测试框架
- **Testing Library** - 组件测试
- **ESLint 9.28+** - 代码检查
- **Prettier** - 代码格式化
- **Husky** - Git hooks
- **lint-staged** - 预提交检查

## 🚀 快速开始

### 环境要求

- Node.js 18.17+
- npm 9+

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看结果。

### 可用脚本

```bash
# 开发
npm run dev          # 启动开发服务器
npm run build        # 构建生产版本
npm run start        # 启动生产服务器

# 代码质量
npm run lint         # ESLint 检查
npm run lint:fix     # 自动修复 ESLint 问题
npm run lint:ai-patterns # AI 反模式检查
npm run typecheck    # TypeScript 类型检查

# 测试
npm run test         # 运行测试
npm run test:watch   # 监听模式运行测试
npm run test:coverage # 生成测试覆盖率报告

# 国际化和翻译
# 翻译功能已移除，如需要请手动配置国际化方案

# 格式化工具链
npm run format:safe      # 安全格式化（只处理代码文件）
npm run format:check     # 检查格式但不修改
npm run demo:formatting  # 演示格式化效果

# 综合检查
npm run check:all    # 运行所有检查（类型、lint、测试）
```

## 📁 项目结构

```
project/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 国际化路由
│   └── globals.css        # 全局样式
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 基础组件
│   ├── interactive/      # 专门交互组件
│   ├── navigation/       # 导航组件
│   ├── mdx/             # MDX 组件
│   └── analytics/       # 分析组件
├── lib/                  # 功能模块
│   ├── analytics/        # 分析服务
│   ├── cms/             # 内容管理
│   ├── email/           # 邮件服务
│   ├── i18n/            # 国际化
│   ├── observability/   # 监控服务
│   ├── security/        # 安全防护
│   ├── seo/             # SEO 工具
│   ├── storage/         # 文件存储
│   ├── rate-limit/      # 速率限制
│   ├── testing/         # 测试工具
│   └── config/          # 配置管理
├── content/              # MDX 内容文件
│   ├── en/              # 英文内容 (默认语言)
│   └── zh/              # 中文内容 (手动维护)
├── docs/                 # 项目文档
│   ├── react-19-component-guidelines.md  # React 19 组件指南
│   └── interactive-components-guide.md   # 交互组件使用指南
├── hooks/                # 自定义 Hooks
├── stores/               # Zustand 状态管理
├── templates/            # 组件模板
├── types/                # TypeScript 类型
├── __tests__/            # 测试文件
├── middleware.ts         # 统一中间件
├── env.ts               # 环境变量配置
└── next.config.ts       # Next.js 配置
```

## 🌍 环境配置

### 基础配置（必需）

```bash
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 分析工具配置（可选 - 三选一或组合使用）

#### PostHog - 开源产品分析平台

```bash
NEXT_PUBLIC_POSTHOG_KEY=phc_your_key
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com
```

#### Google Analytics - 传统网站分析

```bash
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

#### Vercel Analytics - 性能分析

```bash
# Vercel部署时自动启用，无需配置
```

### 其他可选服务

```bash
# 邮件服务
RESEND_TOKEN=re_your_token
RESEND_FROM=<EMAIL>

# 监控服务
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# 安全防护
ARCJET_KEY=ajkey_your_key

# Redis 缓存
UPSTASH_REDIS_REST_URL=https://your-redis-url
UPSTASH_REDIS_REST_TOKEN=your_redis_token

# 文件存储
BLOB_READ_WRITE_TOKEN=your_blob_token
```

## 🧪 开发工作流程

### 日常开发

```bash
# 1. 启动开发环境
npm run dev

# 2. 运行测试（监听模式）
npm run test:watch

# 3. 类型检查
npm run typecheck

# 4. 代码检查和修复
npm run lint:fix
```

### 提交前检查

```bash
# 运行完整检查套件
npm run check:all
```

### 代码质量保证

- ✅ **TypeScript 严格模式** - 完整的类型安全
- ✅ **ESLint 配置** - 代码质量检查
- ✅ **Prettier 格式化** - 统一代码风格
- ✅ **Husky Git Hooks** - 提交前自动检查
- ✅ **Vitest 测试** - 单元测试和组件测试
- ✅ **React Query** - 数据获取最佳实践
- ✅ **Zustand** - 状态管理
- ✅ **shadcn/ui** - 现代化组件库

## 🎉 配置完成状态

### ✅ 已完成配置

1. **基础框架**

   - Next.js 15 + React 19
   - TypeScript 5.8+
   - Tailwind CSS 4.1+

2. **核心依赖**

   - Zustand 状态管理
   - React Query 数据获取
   - React Hook Form + Zod 表单处理
   - Framer Motion 动画
   - Lucide React 图标

3. **UI 组件**

   - shadcn/ui 组件库
   - 响应式设计
   - 深色模式支持

4. **开发工具**

   - Vitest 测试框架
   - ESLint + Prettier
   - Husky Git hooks
   - 环境变量管理

5. **项目结构**
   - 模块化目录结构
   - 类型定义
   - 测试配置

### 🚀 下一步建议

1. **添加更多 UI 组件**

   ```bash
   npx shadcn@latest add input textarea select
   ```

2. **配置国际化中间件**

   - 实现多语言路由
   - 添加语言切换功能

3. **集成分析工具**

   - 配置 PostHog/GA/Vercel Analytics
   - 添加性能监控

4. **添加更多测试**
   - E2E 测试（Playwright）
   - 集成测试
   - 性能测试

## 📚 学习资源

### 框架文档

- [Next.js 15 文档](https://nextjs.org/docs)
- [React 19 文档](https://react.dev)
- [shadcn/ui 组件](https://ui.shadcn.com)
- [Tailwind CSS](https://tailwindcss.com)
- [Zustand 状态管理](https://github.com/pmndrs/zustand)
- [React Query](https://tanstack.com/query/latest)

### 项目文档

- [React 19 组件使用指南](./docs/react-19-component-guidelines.md)
- [交互组件使用指南](./docs/interactive-components-guide.md)
- [useEffect 最佳实践指南](./docs/useEffect-best-practices.md)

- [多语言网站实现总结](./docs/多语言网站实现总结.md)
- [项目技术方案](./docs/项目技术方案.md)

### 格式化工具链文档

- [🎨 格式化工具链完整演示](./docs/husky-lint-staged-prettier-demo.md)
- [🚀 快速开始指南](./docs/quick-start-formatting-toolchain.md)
- [📊 高级配置指南](./docs/formatting-toolchain-advanced-guide.md)
- [🎉 最终实施总结](./docs/formatting-toolchain-final-summary.md)
- [✅ 自动格式化实施完成报告](./docs/auto-formatting-implementation-complete.md)

---

**项目已成功配置完成！🎉**

现在您可以开始开发现代化的 React 应用了。所有核心功能都已配置就绪，包括类型安全、测试、代码质量检查和现代化的开发工具链。
