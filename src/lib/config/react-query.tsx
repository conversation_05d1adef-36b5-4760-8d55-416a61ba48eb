'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';

export function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // 数据保持新鲜时间：5分钟
            staleTime: 5 * 60 * 1000,
            // 缓存时间：10分钟
            gcTime: 10 * 60 * 1000,
            // 重试配置
            retry: (failureCount, error) => {
              // 4xx 错误不重试
              if (error instanceof Error && 'status' in error) {
                const status = (error as Error & { status?: number }).status;
                if (status && status >= 400 && status < 500) {
                  return false;
                }
              }
              // 最多重试 3 次
              return failureCount < 3;
            },
            // 重试延迟
            retryDelay: (attemptIndex) =>
              Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            // 变更重试配置
            retry: 1,
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
