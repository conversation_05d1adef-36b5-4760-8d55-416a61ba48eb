# 文案编辑流程指南

## 目录
- [概述](#概述)
- [工作流程](#工作流程)
- [内容类型](#内容类型)
- [编辑规范](#编辑规范)
- [质量控制](#质量控制)
- [工具使用](#工具使用)
- [常见问题](#常见问题)

## 概述

本文档描述了项目中文案编辑的完整工作流程，包括内容创建、编辑、审核和发布的各个环节。

### 项目文案架构
- **MDX内容文件**：存储在 `content/en/` 目录，用于页面内容
- **界面文案**：存储在 `src/lib/i18n/translations.ts`，用于UI组件
- **支持语言**：英文（源语言）、中文（目标语言）

## 工作流程

### 1. 需求收集阶段
| 步骤 | 负责人 | 输入 | 输出 | 时间要求 |
|------|--------|------|------|----------|
| 需求分析 | 产品经理 | 功能需求文档 | 文案需求清单 | 1-2天 |
| 内容规划 | 内容编辑 | 文案需求清单 | 内容大纲 | 1天 |
| 资源评估 | 项目经理 | 内容大纲 | 工作量评估 | 0.5天 |

### 2. 内容创建阶段

#### 2.1 MDX页面内容创建
```bash
# 1. 在content/en/目录下创建新文件
touch content/en/new-page.mdx

# 2. 使用标准MDX格式
---
title: "页面标题"
description: "页面描述"
date: "2024-01-01"
---

# 页面标题

页面内容...
```

#### 2.2 界面文案创建
```typescript
// 在 src/lib/i18n/translations.ts 中添加新文案
export const enTranslations: Translations = {
  // 现有内容...
  newSection: {
    title: 'New Section Title',
    description: 'Section description',
    button: 'Action Button'
  }
};
```

### 3. 编辑审核阶段
| 检查项 | 标准 | 工具 | 负责人 |
|--------|------|------|--------|
| 语法检查 | 无语法错误 | Grammarly/人工 | 内容编辑 |
| 格式规范 | 符合Markdown规范 | Prettier | 开发者 |
| 内容准确性 | 信息准确无误 | 人工审核 | 内容审核员 |
| 品牌一致性 | 符合品牌调性 | 品牌指南 | 品牌经理 |

### 4. 发布准备阶段
```bash
# 1. 格式检查
npm run format:check

# 2. 内容验证
npm run lint

# 3. 本地预览
npm run dev

# 4. 提交代码
git add .
git commit -m "feat: 添加新页面内容"
git push origin feature/new-content
```

## 内容类型

### 1. 页面内容 (MDX)
- **位置**：`content/en/*.mdx`
- **格式**：MDX (Markdown + JSX)
- **用途**：静态页面内容、博客文章、产品介绍

#### 标准模板
```mdx
---
title: "页面标题"
description: "SEO描述"
keywords: ["关键词1", "关键词2"]
date: "2024-01-01"
author: "作者名"
---

# 主标题

## 二级标题

正文内容支持**粗体**、*斜体*和[链接](https://example.com)。

### 代码示例
```javascript
function example() {
  console.log('Hello World');
}
```

### 列表
- 项目一
- 项目二
- 项目三
```

### 2. 界面文案 (TypeScript)
- **位置**：`src/lib/i18n/translations.ts`
- **格式**：TypeScript对象
- **用途**：按钮、标签、提示信息、导航菜单

#### 组织结构
```typescript
export interface Translations {
  common: {        // 通用文案
    loading: string;
    error: string;
    success: string;
  };
  navigation: {    // 导航文案
    home: string;
    products: string;
  };
  home: {         // 页面特定文案
    title: string;
    subtitle: string;
  };
}
```

## 编辑规范

### 1. 文案风格指南
- **语调**：专业、友好、简洁
- **人称**：统一使用第二人称（您/你）
- **时态**：现在时为主
- **标点**：中英文混排时注意空格

### 2. Markdown规范
```markdown
# 一级标题（页面主标题，每页只有一个）

## 二级标题（章节标题）

### 三级标题（小节标题）

- 无序列表项
- 使用短横线开头

1. 有序列表项
2. 使用数字开头

**粗体文本** 用于强调重要信息
*斜体文本* 用于术语或引用

[链接文本](https://example.com) 
![图片描述](image-path.jpg)

`行内代码` 用于技术术语

```语言
代码块
```
```

### 3. 技术术语处理
| 类型 | 处理方式 | 示例 |
|------|----------|------|
| 产品名称 | 保持原文 | Next.js, React, TypeScript |
| API术语 | 保持原文 | useState, useEffect, props |
| 文件名 | 保持原文 | package.json, README.md |
| 命令行 | 保持原文 | npm install, git commit |

## 质量控制

### 1. 自检清单
- [ ] 内容准确性：信息是否准确无误
- [ ] 语法正确性：是否有语法错误
- [ ] 格式规范性：是否符合Markdown规范
- [ ] 链接有效性：所有链接是否可访问
- [ ] 图片完整性：图片是否正常显示
- [ ] 代码正确性：代码示例是否可运行

### 2. 审核流程
```mermaid
graph TD
    A[内容创建] --> B[自检]
    B --> C[同行评审]
    C --> D{审核通过?}
    D -->|是| E[技术审核]
    D -->|否| F[修改内容]
    F --> B
    E --> G{技术审核通过?}
    G -->|是| H[发布准备]
    G -->|否| I[技术修改]
    I --> E
    H --> J[正式发布]
```

### 3. 验证命令
```bash
# 格式检查
npm run format:check

# 链接检查（如果配置了）
npm run check:links

# 拼写检查（如果配置了）
npm run check:spelling

# 构建测试
npm run build
```

## 工具使用

### 1. 开发环境
- **编辑器**：VS Code + Markdown扩展
- **预览**：Markdown Preview Enhanced
- **格式化**：Prettier
- **版本控制**：Git

### 2. 推荐扩展
```json
{
  "recommendations": [
    "yzhang.markdown-all-in-one",
    "shd101wyy.markdown-preview-enhanced",
    "esbenp.prettier-vscode",
    "streetsidesoftware.code-spell-checker"
  ]
}
```

### 3. 本地预览
```bash
# 启动开发服务器
npm run dev

# 访问页面
open http://localhost:3000
```

## 常见问题

### Q1: 如何添加新的页面内容？
**A**: 在 `content/en/` 目录下创建新的 `.mdx` 文件，使用标准的frontmatter格式。

### Q2: 如何修改界面文案？
**A**: 编辑 `src/lib/i18n/translations.ts` 文件中对应的文案条目。

### Q3: 图片应该放在哪里？
**A**: 静态图片放在 `public/` 目录下，在Markdown中使用相对路径引用。

### Q4: 如何处理代码示例？
**A**: 使用三个反引号包围代码块，并指定语言类型以获得语法高亮。

### Q5: 内容修改后多久生效？
**A**: 开发环境立即生效，生产环境需要重新构建和部署。

## 相关文档

- [翻译工作流程](./翻译工作流程.md) - 了解如何进行内容翻译
- [国际化配置指南](./国际化配置指南.md) - 了解技术配置和维护

---

**文档维护**：本文档由内容团队维护，如有问题请联系 [内容负责人]。
**最后更新**：2024年1月
